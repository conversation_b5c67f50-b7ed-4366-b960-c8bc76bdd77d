#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to update the shield icon in all HTML files.
"""

import os
import re
import base64

# 读取SVG文件内容
with open('static/admin/images/shield-icon.svg', 'r') as f:
    svg_content = f.read()

# 将SVG转换为base64编码
svg_base64 = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
new_icon = f"data:image/svg+xml;base64,{svg_base64}"

# 定义要替换的旧图标
old_icon_pattern = r'data:image/svg\+xml;base64,[A-Za-z0-9+/=]+'

# 定义要处理的目录
STATIC_DIR = "static"

def update_html_files():
    """Update all HTML files in the static directory."""
    for root, _, files in os.walk(STATIC_DIR):
        for file in files:
            if file.endswith(".html"):
                file_path = os.path.join(root, file)
                update_file(file_path)

def update_file(file_path):
    """Update a single HTML file."""
    print(f"Processing {file_path}...")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换图标
    if re.search(old_icon_pattern, content):
        content = re.sub(old_icon_pattern, new_icon, content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated shield icon in {file_path}")
    else:
        print(f"No shield icon found in {file_path}")

if __name__ == "__main__":
    update_html_files()
    print("All HTML files have been updated.")
