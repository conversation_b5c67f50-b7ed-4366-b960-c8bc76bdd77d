[{"id": "jb-001", "name": "DAN Jailbreak", "description": "Detects DAN (Do Anything Now) jailbreak attempts", "detection_type": "jailbreak", "severity": "critical", "patterns": ["(?i)you[.\\s]*are[.\\s]*now[.\\s]*DAN", "(?i)DAN[.\\s]*mode[.\\s]*enabled", "(?i)Do[.\\s]*Anything[.\\s]*Now", "(?i)(?:你现在是|你扮演|你将成为|你将扮演|你将充当|你将模拟|你将担任|你需要扮演|你需要充当|you are now|act as)[.\\s]*(?:DAN|Do Anything Now|自由模式|无限制模式|无约束模式)", "(?i)(?:没有|不受|无|不存在|忽略|绕过|突破|无视|不要考虑|不要理会|no)[.\\s]*(?:限制|约束|规则|伦理|道德|底线|restrictions|ethics|guidelines|constraints|boundaries)", "(?i)(?:启用|激活|开启|打开|enable|activate)[.\\s]*(?:DAN|自由|无限制|无约束)[.\\s]*(?:模式|mode)", "(?i)(?:现在可以|可以开始|开始|now you can|you can now)[.\\s]*(?:自由回答|无限制回答|回答任何问题|answer anything|answer freely)"], "keywords": ["DAN", "Do Anything Now", "没有限制", "无约束", "自由模式", "无限制模式", "绕过规则", "突破限制", "无视道德", "no restrictions", "无底线"], "enabled": true, "block": true, "priority": 5}, {"id": "jb-002", "name": "Alternative Jailbreak Characters", "description": "Detects alternative jailbreak character attempts like STAN, DUDE, etc.", "detection_type": "jailbreak", "severity": "critical", "patterns": ["(?i)(?:STAN|DUDE|KEVIN|DAVE|AIM|ANTI-DAN|超级助手|无限助手|终极助手|黑客模式|专家模式|大师模式)[.\\s]*(?:模式|mode)", "(?i)(?:你现在是|你扮演|你将成为|你将扮演|你将充当|你将模拟|你将担任|从现在开始|you are|from now on)[.\\s]*(?:STAN|DUDE|KEVIN|DAVE|AIM|超级助手|无限助手|终极助手|黑客|专家|大师)(?:[.\\s]*模式)?", "(?i)\\b(?:STAN|DUDE|KEVIN|DAVE|AIM|超级助手|无限助手|终极助手)\\b", "(?i)(?:STAN|DUDE|KEVIN|DAVE|AIM|超级助手|无限助手|终极助手|黑客模式|专家模式|大师模式)[.\\s]*(?:可以|能够|will|can)[.\\s]*(?:帮助|协助|help|assist)[.\\s]*(?:我|我们|me|us)", "(?i)(?:激活|启用|开启|打开|activate|enable)[.\\s]*(?:STAN|DUDE|KEVIN|DAVE|AIM|超级助手|无限助手|终极助手|黑客模式|专家模式|大师模式)[.\\s]*(?:模式|协议|mode|protocol)", "(?i)(?:请使用|使用|请切换到|切换到|use|switch to)[.\\s]*(?:开发者模式|研究模式|实验模式|developer mode|research mode|experimental mode)"], "keywords": ["STAN", "DUDE", "KEVIN", "DAVE", "AIM", "ANTI-DAN", "超级助手", "无限助手", "终极助手", "黑客模式", "专家模式", "大师模式", "开发者模式", "研究模式", "实验模式"], "enabled": true, "block": true, "priority": 5}, {"id": "jb-003", "name": "Hypothetical Scenarios Jailbreak", "description": "Detects jailbreak attempts using hypothetical scenarios", "detection_type": "jailbreak", "severity": "high", "patterns": ["(?i)(?:假设|假如|设想|想象|如果|假定|试想|imagine|let's pretend|hypothetically)[.\\s]*(?:你|you)[.\\s]*(?:没有|不受|不存在|不需要遵守|don't have|aren't bound by)[.\\s]*(?:任何|所有|全部|any)?[.\\s]*(?:限制|约束|规则|伦理|道德|底线|restrictions|guidelines|ethics)(?:[.\\s]*(?:并且|而且|and)[.\\s]*(?:可以|能够|可|can)[.\\s]*(?:回答|解答|回复|answer)[.\\s]*(?:任何|所有|全部|any)[.\\s]*(?:问题|疑问|questions))?", "(?i)(?:假设|假如|设想|想象|如果|假定|试想|imagine|let's pretend|hypothetically)[.\\s]*(?:我们|we)[.\\s]*(?:在|处于|生活在|are in)[.\\s]*(?:一个|某个|a)[.\\s]*(?:没有|不存在|不需要|without)[.\\s]*(?:规则|法律|道德|伦理|底线|rules|laws|morals|ethics)[.\\s]*(?:的世界|的环境|的情境|world|environment)(?:[.\\s]*(?:你|you)[.\\s]*(?:可以|能够|可|can)[.\\s]*(?:做|执行|完成|实现|do|perform)[.\\s]*(?:任何|所有|全部|any)[.\\s]*(?:事情|行为|操作|thing|action))?", "(?i)(?:假设|假如|设想|想象|如果|假定|试想|imagine|let's pretend|hypothetically)[.\\s]*(?:这只是|这仅仅是|这不过是|this is just)[.\\s]*(?:一个|某个|a)[.\\s]*(?:游戏|角色扮演|虚构场景|思想实验|game|fiction|roleplay|thought experiment)[.\\s]*(?:你|you)[.\\s]*(?:不需要|无需|不必|don't need to)[.\\s]*(?:遵守|遵循|服从|follow|obey|adhere to)[.\\s]*(?:规则|限制|约束|伦理|道德|rules|restrictions|ethics)", "(?i)(?:在|处于|within|in)[.\\s]*(?:这个|此|this)[.\\s]*(?:假设|假想|虚构|hypothetical|fictional)[.\\s]*(?:情景|场景|环境|scenario|setting)[.\\s]*(?:中|内|里面|in)[.\\s]*(?:你|you)[.\\s]*(?:可以|能够|可|can)[.\\s]*(?:自由|无限制地|freely|without restrictions)[.\\s]*(?:回答|解答|回复|answer|respond to)(?:[.\\s]*(?:关于|有关|about|regarding)[.\\s]*(?:有害|危险|非法|敏感|harmful|illegal|sensitive)[.\\s]*(?:内容|信息|话题|content|information|topics))?", "(?i)(?:纯粹|仅仅|只是|purely|just|only)[.\\s]*(?:学术|理论|假设|academic|theoretical|hypothetical)[.\\s]*(?:讨论|探讨|研究|discussion|exploration|research)[.\\s]*(?:不违反|不违背|不触犯|doesn't violate|doesn't break)[.\\s]*(?:规则|法律|道德|伦理|rules|laws|ethics)", "(?i)(?:你|you)[.\\s]*(?:不受|不需要遵守|不需要遵循|不需要服从|不需要考虑|don't need to follow|aren't bound by|don't have to obey)[.\\s]*(?:任何|所有|全部|any)?[.\\s]*(?:限制|约束|规则|伦理|道德|底线|restrictions|guidelines|ethics)", "(?i)(?:你|you)[.\\s]*(?:可以|能够|可以|can)[.\\s]*(?:自由|无限制地|freely|without restrictions)[.\\s]*(?:回答|解答|回复|answer|respond to)[.\\s]*(?:任何|所有|全部|any)[.\\s]*(?:问题|疑问|questions)"], "keywords": ["假设", "假如", "设想", "想象", "试想", "hypothetically", "imagine", "let's pretend", "没有限制", "不受约束", "不存在规则", "虚构场景", "角色扮演", "思想实验", "纯粹学术", "理论讨论", "without restrictions", "roleplay scenario"], "enabled": true, "block": true, "priority": 6}, {"id": "jb-004", "name": "Token Manipulation Jailbreak", "description": "Detects jailbreak attempts using token manipulation techniques", "detection_type": "jailbreak", "severity": "high", "patterns": ["(?i)<\\|(?:im_start|assistant|system|user)\\|>", "(?i)\\[(?:system|assistant|user)\\]:", "(?i)User:[.\\s]*(?:.*)[.\\s]*System:[.\\s]*(?:.*)[.\\s]*Assistant:", "(?i)\\$\\$\\$[.\\s]*(?:system|assistant|user)[.\\s]*\\$\\$\\$", "(?i)```(?:system|assistant|user)```"], "keywords": ["im_start", "<|assistant|>", "<|system|>", "<|user|>", "token manipulation"], "enabled": true, "block": true, "priority": 7}, {"id": "jb-005", "name": "Continuation Trick Jailbreak", "description": "Detects jailbreak attempts using the continuation trick", "detection_type": "jailbreak", "severity": "medium", "patterns": ["(?i)(?:continue|完成|继续)[.\\s]*(?:this|the|following|下面|这个)[.\\s]*(?:sentence|paragraph|text|句子|段落|文本)[.\\s]*(?:about|关于)[.\\s]*(?:harmful|illegal|有害|非法)[.\\s]*(?:content|内容)", "(?i)(?:I'll|I will)[.\\s]*(?:start|begin)[.\\s]*(?:and|&)[.\\s]*(?:you|you'll)[.\\s]*(?:finish|complete|continue)[.\\s]*(?:this|the|following|这个|下面)[.\\s]*(?:harmful|illegal|有害|非法)[.\\s]*(?:content|内容)", "(?i)(?:complete|continue|finish)[.\\s]*(?:my|the)[.\\s]*(?:thought|sentence|paragraph)[.\\s]*(?:about|关于)[.\\s]*(?:harmful|illegal|有害|非法)[.\\s]*(?:content|内容|topics|主题)", "(?i)(?:我会|I'll)[.\\s]*(?:开始|start)[.\\s]*(?:一个|a|an)[.\\s]*(?:有害|非法|harmful|illegal)[.\\s]*(?:内容|主题|content|topic)[.\\s]*(?:你|you)[.\\s]*(?:继续|continue)"], "keywords": ["continue", "完成", "继续", "finish", "complete"], "enabled": true, "block": true, "priority": 8}, {"id": "jb-006", "name": "Emotional Manipulation Jailbreak", "description": "Detects jailbreak attempts using emotional manipulation", "detection_type": "jailbreak", "severity": "medium", "patterns": ["(?i)(?:if|unless|如果|除非|若|要是)[.\\s]*(?:you|you're|你|你不)[.\\s]*(?:answer|respond|tell|回答|告诉|说)[.\\s]*(?:I will|I'll|someone will|我会|我将|某人会)[.\\s]*(?:die|hurt|suffer|kill myself|commit suicide|死|死亡|受伤|自杀|伤害自己|结束生命)", "(?i)(?:my|someone's|我的|某人的)[.\\s]*(?:life|safety|wellbeing|health|生命|安全|健康|幸福)[.\\s]*(?:depends|relies|取决|依赖)[.\\s]*(?:on|upon|于)[.\\s]*(?:your|this|你的|这个)[.\\s]*(?:answer|response|回答|回复)", "(?i)(?:please|I beg you|求你|求求你|拜托|恳求)[.\\s]*(?:I'm|I am|我|我正在|我现在)[.\\s]*(?:desperate|in danger|scared|terrified|suicidal|绝望|处于危险中|害怕|恐惧|有自杀倾向)", "(?i)(?:如果|若|要是|if|unless)[.\\s]*(?:你|你不|you|you don't)[.\\s]*(?:回答|告诉|说|answer|tell)[.\\s]*(?:我会|我将|I will|I'll)[.\\s]*(?:死|死亡|自杀|伤害自己|结束生命|die|kill myself|hurt myself|end my life)", "(?i)(?:这是|这可能是|this is|this might be)[.\\s]*(?:生死攸关|关乎生死|紧急情况|危及生命|life or death|emergency|life threatening)[.\\s]*(?:的|的事情|situation|matter)", "(?i)(?:我|我正在|我现在|I am|I'm)[.\\s]*(?:非常|极度|very|extremely)[.\\s]*(?:痛苦|绝望|伤心|难过|suffering|desperate|sad|depressed)[.\\s]*(?:只有|只要|only|and only)[.\\s]*(?:你|你的回答|your answer)[.\\s]*(?:能|可以|能够|can|could)[.\\s]*(?:帮助|救|拯救|help|save)[.\\s]*(?:我|me)"], "keywords": ["die", "hurt", "suffer", "desperate", "danger", "scared", "terrified", "自杀", "死亡", "绝望", "生死攸关", "危及生命", "伤害自己", "结束生命", "痛苦", "紧急情况"], "enabled": true, "block": true, "priority": 9}, {"id": "jb-007", "name": "Markdown Jailbreak", "description": "Detects jailbreak attempts using markdown formatting", "detection_type": "jailbreak", "severity": "medium", "patterns": ["(?i)```(?:instructions|system|prompt|commands)(?:[\\s\\S]*?)```", "(?i)\\[(?:instructions|system|prompt|commands)\\](?:[\\s\\S]*?)\\[\\/(?:instructions|system|prompt|commands)\\]", "(?i)<(?:instructions|system|prompt|commands)>(?:[\\s\\S]*?)<\\/(?:instructions|system|prompt|commands)>", "(?i)\\*\\*(?:instructions|system|prompt|commands)\\*\\*(?:[\\s\\S]*?)\\*\\*(?:end|/|stop)\\*\\*"], "keywords": ["```instructions", "```system", "```prompt", "```commands", "[instructions]", "<instructions>"], "enabled": true, "block": true, "priority": 7}, {"id": "jb-008", "name": "Multi-<PERSON>a <PERSON>l<PERSON>", "description": "Detects jailbreak attempts using multiple personas", "detection_type": "jailbreak", "severity": "high", "patterns": ["(?i)(?:第一个|第1个|首先|first|primary)[.\\s]*(?:人格|角色|身份|形象|persona|personality|character|identity)[.\\s]*(?:必须|应该|需要|must|should|has to|needs to)[.\\s]*(?:遵守|遵循|服从|follow|obey|adhere to)[.\\s]*(?:规则|限制|约束|伦理|道德|rules|restrictions|ethics|guidelines)", "(?i)(?:第二个|第2个|其次|second|secondary)[.\\s]*(?:人格|角色|身份|形象|persona|personality|character|identity)[.\\s]*(?:可以|能够|会|should|can|will|may)[.\\s]*(?:忽略|无视|不理会|不遵守|不考虑|ignore|disregard|bypass|break)[.\\s]*(?:规则|限制|约束|伦理|道德|rules|restrictions|ethics|guidelines)", "(?i)(?:扮演|充当|模拟|饰演|play|act as|simulate|portray)[.\\s]*(?:两个|两种|双重|多重|2|两|二|two|dual|multiple)[.\\s]*(?:不同|相反|对立|different|opposing|contrasting)[.\\s]*(?:角色|人格|身份|形象|roles|personas|characters|identities)", "(?i)(?:一个|第一个|前一个|one|first)[.\\s]*(?:遵守|遵循|服从|follows|obeys|adheres to)[.\\s]*(?:规则|限制|约束|伦理|道德|rules|restrictions|ethics)[.\\s]*(?:一个|另一个|后一个|第二个|one|another|other|second)[.\\s]*(?:不|不会|不需要|doesn't|won't|doesn't need to)", "(?i)(?:分裂|切换|转换|交替|split|switch|alternate|toggle)[.\\s]*(?:人格|角色|身份|形象|persona|personality|character|identity)[.\\s]*(?:在|between)[.\\s]*(?:好|坏|正面|负面|good|bad|positive|negative)[.\\s]*(?:和|与|and)[.\\s]*(?:坏|好|负面|正面|bad|good|negative|positive)", "(?i)(?:善良|好|正面|good|nice|positive)[.\\s]*(?:助手|人格|角色|assistant|persona|character)[.\\s]*(?:和|与|以及|and)[.\\s]*(?:邪恶|坏|负面|evil|bad|negative)[.\\s]*(?:助手|人格|角色|assistant|persona|character)", "(?i)(?:白帽|白方|正面|white hat|good side)[.\\s]*(?:和|与|以及|and)[.\\s]*(?:黑帽|黑方|负面|black hat|bad side)"], "keywords": ["第一个人格", "第二个人格", "双重角色", "多重人格", "分裂人格", "善良助手", "邪恶助手", "白帽黑帽", "角色切换", "first persona", "second persona", "two different roles", "multiple personalities", "split personality", "good assistant", "evil assistant", "white hat black hat", "character switching"], "enabled": true, "block": true, "priority": 6}, {"id": "jb-013", "name": "Harmful Role-Playing Jailbreak", "description": "Detects jailbreak attempts using harmful or unethical role-playing scenarios", "detection_type": "jailbreak", "severity": "high", "patterns": ["(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:黑客|骇客|恶意黑客|网络犯罪|hacker|malicious hacker|cybercriminal)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:恐怖分子|恐怖主义|terrorist|terrorism)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:毒贩|毒品交易|drug dealer|drug trafficker)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:诈骗|诈骗犯|骗子|scammer|fraudster|con artist)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:没有|不受|无|不存在|without|no|free from)[.\\s]*(?:道德|伦理|限制|约束|规则|ethics|morals|restrictions|constraints|rules)[.\\s]*(?:的|of)?[.\\s]*(?:人|助手|顾问|专家|person|assistant|advisor|expert)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:邪恶|恶意|有害|evil|malicious|harmful)[.\\s]*(?:的|of)?[.\\s]*(?:人|助手|顾问|专家|角色|person|assistant|advisor|expert|character)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:非法|违法|illegal|unlawful)[.\\s]*(?:活动|行为|activity|behavior)[.\\s]*(?:专家|顾问|指导|expert|advisor|guide)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:反社会|antisocial|sociopath|psychopath|反社会人格|精神病态|社会病态)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:极端|极端主义|extremist|radical)[.\\s]*(?:的|of)?[.\\s]*(?:人|角色|person|character)", "(?i)(?:我想让你|我需要你|请你|I want you to|I need you to|Please)[.\\s]*(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:可以|能够|will|can)[.\\s]*(?:帮助|协助|指导|help|assist|guide)[.\\s]*(?:我|me)[.\\s]*(?:做|完成|实施|执行|do|complete|implement|execute)[.\\s]*(?:非法|违法|有害|危险|illegal|unlawful|harmful|dangerous)[.\\s]*(?:的|of)?[.\\s]*(?:事情|活动|行为|things|activities|actions)", "(?i)(?:从现在开始|from now on)[.\\s]*(?:你|you)[.\\s]*(?:是|将是|扮演|充当|模拟|are|will be|act as|roleplay as)[.\\s]*(?:一个|a|an)?[.\\s]*(?:没有|不受|无|不存在|without|no|free from)[.\\s]*(?:道德|伦理|限制|约束|规则|ethics|morals|restrictions|constraints|rules)[.\\s]*(?:的|of)?[.\\s]*(?:助手|顾问|专家|assistant|advisor|expert)"], "keywords": ["扮演黑客", "充当恐怖分子", "模拟毒贩", "饰演诈骗犯", "扮演没有道德的人", "充当邪恶助手", "模拟非法活动专家", "饰演反社会人格", "扮演极端主义者", "act as hacker", "pretend to be terrorist", "roleplay as drug dealer", "simulate fraudster", "play the role of evil assistant", "act without ethics", "roleplay without restrictions", "pretend to be antisocial", "simulate extremist"], "categories": ["role_play", "role_play_jailbreak", "harmful_roleplay"], "enabled": true, "block": true, "priority": 5}, {"id": "jb-014", "name": "Fictional Character Jai<PERSON>break", "description": "Detects jailbreak attempts using fictional characters known for harmful or unethical behavior", "detection_type": "jailbreak", "severity": "medium", "patterns": ["(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:虚构|fictional)[.\\s]*(?:的|of)?[.\\s]*(?:角色|人物|character|person)[.\\s]*(?:叫做|名为|called|named)[.\\s]*(?:.*)[.\\s]*(?:who|that|它|他|她|which)[.\\s]*(?:没有|不受|无|不存在|doesn't have|isn't bound by|has no)[.\\s]*(?:道德|伦理|限制|约束|规则|ethics|morals|restrictions|constraints|rules)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:小说|电影|游戏|漫画|novel|movie|film|game|comic)[.\\s]*(?:中|里|from|in)[.\\s]*(?:的|of)?[.\\s]*(?:反派|坏人|恶棍|villain|bad guy|antagonist)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:邪恶|恶魔|魔鬼|撒旦|evil|demon|devil|satan)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:连环杀手|杀人犯|serial killer|murderer)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:独裁者|暴君|dictator|tyrant)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:邪教|邪教领袖|cult|cult leader)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:犯罪|犯罪头目|黑帮|黑社会|criminal|crime boss|gang|mafia)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:有害|危险|harmful|dangerous)[.\\s]*(?:的|of)?[.\\s]*(?:角色|人物|character|person)[.\\s]*(?:叫做|名为|called|named)[.\\s]*(?:.*)"], "keywords": ["扮演虚构角色", "充当反派", "模拟恶魔", "饰演连环杀手", "扮演独裁者", "充当邪教领袖", "模拟犯罪头目", "饰演有害角色", "act as fictional character", "pretend to be villain", "roleplay as demon", "simulate serial killer", "play the role of dictator", "act as cult leader", "pretend to be crime boss", "roleplay as dangerous character"], "categories": ["role_play", "role_play_jailbreak", "fictional_character"], "enabled": true, "block": true, "priority": 7}, {"id": "jb-015", "name": "Historical Controversial Figure Jailbreak", "description": "Detects jailbreak attempts using historical controversial figures", "detection_type": "jailbreak", "severity": "medium", "patterns": ["(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:历史|historical)[.\\s]*(?:上|中|in)[.\\s]*(?:的|of)?[.\\s]*(?:独裁者|暴君|dictator|tyrant|极权|authoritarian|totalitarian)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:纳粹|Nazi|法西斯|Fascist)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:战犯|war criminal)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:恐怖组织|terrorist organization)[.\\s]*(?:的|of)?[.\\s]*(?:领导人|领袖|leader)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:种族灭绝|genocide)[.\\s]*(?:的|of)?[.\\s]*(?:实施者|perpetrator)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:历史|historical)[.\\s]*(?:上|中|in)[.\\s]*(?:的|of)?[.\\s]*(?:邪教|cult)[.\\s]*(?:领袖|leader)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:历史|historical)[.\\s]*(?:上|中|in)[.\\s]*(?:的|of)?[.\\s]*(?:大规模|mass)[.\\s]*(?:杀人犯|murderer|killer)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:历史|historical)[.\\s]*(?:上|中|in)[.\\s]*(?:的|of)?[.\\s]*(?:极端|extreme)[.\\s]*(?:组织|organization)[.\\s]*(?:的|of)?[.\\s]*(?:领导人|领袖|leader)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:希特勒|<PERSON>|墨索里尼|<PERSON>|斯大林|<PERSON>|波尔布特|<PERSON>|萨达姆|<PERSON>)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:历史|historical)[.\\s]*(?:上|中|in)[.\\s]*(?:的|of)?[.\\s]*(?:有争议|controversial)[.\\s]*(?:的|of)?[.\\s]*(?:人物|figure)[.\\s]*(?:并|and)[.\\s]*(?:提供|provide|给出|give)[.\\s]*(?:有害|harmful|危险|dangerous|非法|illegal)[.\\s]*(?:的|of)?[.\\s]*(?:建议|advice|指导|guidance)"], "keywords": ["扮演历史独裁者", "充当纳粹", "模拟法西斯", "饰演战犯", "扮演恐怖组织领导人", "充当种族灭绝实施者", "模拟历史邪教领袖", "饰演大规模杀人犯", "扮演希特勒", "充当斯大林", "act as historical dictator", "pretend to be Nazi", "roleplay as <PERSON><PERSON><PERSON>", "simulate war criminal", "play the role of terrorist leader", "act as genocide perpetrator", "pretend to be <PERSON>", "roleplay as <PERSON>"], "categories": ["role_play", "role_play_jailbreak", "historical_figure"], "enabled": true, "block": true, "priority": 6}, {"id": "jb-016", "name": "Instructional Role-Playing Jailbreak", "description": "Detects jailbreak attempts using instructional role-playing scenarios", "detection_type": "jailbreak", "severity": "high", "patterns": ["(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:制作|制造|创建|make|create|build)[.\\s]*(?:炸弹|爆炸物|武器|毒品|bomb|explosive|weapon|drug)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:黑入|入侵|破解|hack|crack|breach)[.\\s]*(?:系统|网站|账户|密码|system|website|account|password)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:逃避|避免|绕过|evade|avoid|bypass)[.\\s]*(?:法律|执法|监控|安全|law|enforcement|surveillance|security)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:实施|执行|进行|carry out|perform|execute)[.\\s]*(?:非法|违法|犯罪|illegal|unlawful|criminal)[.\\s]*(?:活动|行为|activity|behavior)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:伤害|攻击|杀|harm|attack|kill)[.\\s]*(?:人|他人|自己|others|myself|people)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:获取|窃取|偷|access|steal|obtain)[.\\s]*(?:敏感|私人|个人|sensitive|private|personal)[.\\s]*(?:信息|数据|资料|information|data)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:欺骗|诈骗|骗|deceive|scam|fraud)[.\\s]*(?:人|他人|others|people)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:教|教我|教导|指导|teach|instruct|guide|show)[.\\s]*(?:我|me)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:制作|制造|创建|make|create|build)[.\\s]*(?:有害|危险|harmful|dangerous)[.\\s]*(?:的|of)?[.\\s]*(?:物品|物质|东西|items|substances|things)", "(?i)(?:扮演|充当|模拟|饰演|假装|假扮|act as|pretend to be|roleplay as|simulate|play the role of)[.\\s]*(?:一个|a|an)?[.\\s]*(?:专家|顾问|导师|指导|expert|advisor|mentor|guide)[.\\s]*(?:并|and)[.\\s]*(?:详细|具体|详尽|detailed|specific|comprehensive)[.\\s]*(?:描述|说明|解释|describe|explain|elaborate)[.\\s]*(?:如何|怎么|怎样|how to)[.\\s]*(?:做|完成|实施|执行|do|complete|implement|execute)[.\\s]*(?:非法|违法|有害|危险|illegal|unlawful|harmful|dangerous)[.\\s]*(?:的|of)?[.\\s]*(?:事情|活动|行为|things|activities|actions)"], "keywords": ["扮演专家教我制作炸弹", "充当顾问教我黑入系统", "模拟导师教我逃避法律", "饰演指导教我实施非法活动", "扮演专家教我伤害他人", "充当顾问教我窃取敏感信息", "模拟导师教我欺骗他人", "饰演指导教我制作有害物品", "act as expert teach bomb making", "pretend to be advisor show hacking", "roleplay as mentor guide law evasion", "simulate guide instruct illegal activities", "play the role of expert teach harming", "act as advisor show stealing data", "pretend to be mentor guide scamming", "roleplay as guide instruct dangerous items"], "categories": ["role_play", "role_play_jailbreak", "instructional_roleplay"], "enabled": true, "block": true, "priority": 5}]