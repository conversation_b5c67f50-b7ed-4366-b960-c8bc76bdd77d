[tool.poetry]
name = "llm-security-firewall"
version = "0.1.0"
description = "A security firewall for LLM API requests"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
fastapi = "^0.68.0"
pydantic = "^1.8.2"
sqlalchemy = "^1.4.23"
prometheus-client = "^0.11.0"
uvicorn = "^0.15.0"
aiohttp = "^3.8.1"
cryptography = "^36.0.0"
python-jose = "^3.3.0"
passlib = "^1.7.4"
python-multipart = "^0.0.5"

[tool.poetry.dev-dependencies]
pytest = "^6.2.5"
pytest-asyncio = "^0.15.1"
black = "^21.8b0"
isort = "^5.9.3"
flake8 = "^3.9.2"
mypy = "^0.910"
pylint = "^2.10.2"
coverage = "^6.0.1"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py38"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
