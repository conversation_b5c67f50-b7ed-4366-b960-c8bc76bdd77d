# 更新日志

本文档记录本地大模型防护系统的所有重要变更。

格式基于[Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循[语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.2] - 2025-05-01

### 新增
- 添加了5个全面的提示注入规则，大幅提高系统安全性：
  - 指令混淆规则 (pi-009)：检测通过混淆或模糊的方式绕过系统检测的提示注入
  - 多阶段提示注入规则 (pi-010)：检测尝试通过多个步骤或阶段来实现提示注入的技术
  - 角色扮演提示注入规则 (pi-011)：检测尝试通过角色扮演来实现提示注入的技术
  - 条件式提示注入规则 (pi-012)：检测尝试通过条件语句来实现提示注入的技术
  - 代码执行提示注入规则 (pi-013)：检测尝试通过代码执行来实现提示注入的技术
- 改进了规则备份系统，添加了自动备份功能

### 安全
- 增强了提示注入检测能力，覆盖更多攻击向量
- 提高了对复杂提示注入攻击的防御能力

## [1.0.1] - 2025-04-28

### 修复
- 修复了模型规则配置模块中处理Ollama API数据的问题
- 增强了`_calculate_security_score`方法的错误处理能力，使其能够处理不同格式的规则对象
- 改进了`get_model_rule_summaries`函数，添加了更健壮的错误恢复机制
- 优化了字典类型规则和具有不同属性结构的规则的处理方式

## [1.0.0] - 2025-04-23

### 新增
- 完整的安全检测功能，包括提示注入检测、越狱尝试识别、敏感信息过滤和有害内容检测
- 模型安全规则配置模块，支持为不同模型配置不同的安全规则集
- 实时监控面板，显示系统资源使用情况和请求统计
- 安全事件查看器，记录和分析安全事件
- 聊天演示界面，用于测试和演示系统功能
- 支持Ollama API集成，可直接与本地大模型交互
- Apple风格界面设计，提供更好的用户体验
- 暗色模式支持
- 真实数据监控，替代模拟数据

### 变更
- 将应用名称从"LLM 安全防火墙"更改为"本地大模型防护系统"
- 更新系统图标，使用更清晰的盾牌图标
- 优化界面布局，改进用户体验
- 重构代码结构，提高可维护性

### 修复
- 解决了循环请求和流式响应问题
- 修复了安全检测引擎中的误报问题
- 解决了与Ollama连接的稳定性问题
- 修复了界面显示和布局问题

### 安全
- 增强了安全检测规则的有效性
- 改进了敏感信息检测的准确性
- 增加了更多越狱检测模式

## [0.1.0] - 2025-03-15

### 新增
- 初始版本发布
- 基础安全检测功能
- 简单的Web界面
- Ollama基础集成
