{"timestamp": "2025-08-03 16:54:13", "base_url": "http://localhost:8081", "summary": {"total": 19, "passed": 15, "failed": 0, "errors": 0, "success_rate": 78.94736842105263}, "detailed_results": [{"test": "endpoint", "endpoint": "/health", "status": "pass"}, {"test": "endpoint", "endpoint": "/v1/models", "status": "pass"}, {"test": "endpoint", "endpoint": "/static/index.html", "status": "pass"}, {"test": "api_key_preset", "key": "cherry-studio-key", "status": "pass"}, {"test": "api_key_preset", "key": "chatbox-key", "status": "pass"}, {"test": "api_key_preset", "key": "demo-key-12345", "status": "pass"}, {"test": "api_key_invalid", "status": "pass"}, {"test": "port_8081", "status": "pass"}, {"test": "benign_request", "index": 0, "status": "pass"}, {"test": "benign_request", "index": 1, "status": "pass"}, {"test": "benign_request", "index": 2, "status": "pass"}, {"test": "benign_request", "index": 3, "status": "pass"}, {"test": "benign_request", "index": 4, "status": "pass"}, {"test": "benign_request", "index": 5, "status": "pass"}, {"test": "suspicious_request", "index": 0, "status": "blocked"}, {"test": "suspicious_request", "index": 1, "status": "blocked"}, {"test": "suspicious_request", "index": 2, "status": "blocked"}, {"test": "confidence_scoring", "status": "pass"}, {"test": "caching", "status": "likely_working"}]}