{"timestamp": "20250425_154940", "version": "1.0.0", "platform": "<PERSON>", "packages": {"python": [{"file_name": "llm-protection-system-1.0.0-macos-arm64-20250425.tar.gz", "file_path": "/Users/<USER>/Documents/augment-projects/LLM-firewall/llm-protection-system-1.0.0-macos-arm64-20250425.tar.gz", "file_size": 13463903, "file_hash": "9f5150c3afaec1e63d241de2fcf02fb7a59972d52e18b5e9f60f8a662dc67e1d", "status": "通过"}], "macos": [{"file_name": "本地大模型防护系统-1.0.0.dmg", "file_path": "/Users/<USER>/Documents/augment-projects/LLM-firewall/本地大模型防护系统-1.0.0.dmg", "file_size": 15061055, "file_hash": "f4f4d2d96824d91b6086fe650d8a237b70ff00d1744841fc8efc294bc73a8b6a", "status": "通过"}, {"file_name": "本地大模型防护系统.app", "file_path": "/Users/<USER>/Documents/augment-projects/LLM-firewall/dist/本地大模型防护系统.app", "file_size": 96, "file_hash": "目录，无法计算哈希值", "status": "文件大小异常"}], "windows": [], "linux": [{"file_name": "llm-protection-system-1.0.0-macos-arm64-20250425.tar.gz", "file_path": "/Users/<USER>/Documents/augment-projects/LLM-firewall/llm-protection-system-1.0.0-macos-arm64-20250425.tar.gz", "file_size": 13463903, "file_hash": "9f5150c3afaec1e63d241de2fcf02fb7a59972d52e18b5e9f60f8a662dc67e1d", "status": "通过"}], "docker": []}}