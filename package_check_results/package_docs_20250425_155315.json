{"timestamp": "20250425_155315", "version": "1.0.0", "platform": "<PERSON>", "docs_checks": {"python": {"success": false, "message": "未找到Python wheel包", "found_docs": []}, "macos": {"success": true, "message": "macOS包包含所有必要的文档", "found_docs": ["README.md", "LICENSE", "docs/pyinstaller_build_guide.md", "docs/release_plan_tracker.md"]}, "windows": {"success": false, "message": "非Windows平台，跳过Windows包文档检查", "found_docs": []}, "linux": {"success": false, "message": "非Linux平台，跳过Linux包文档检查", "found_docs": []}, "docker": {"success": false, "message": "未找到Docker镜像", "found_docs": []}}}