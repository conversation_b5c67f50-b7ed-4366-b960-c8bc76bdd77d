<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配色效果展示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .title {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .section {
            background: #fafafa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
        }
        
        .section h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }
        
        .message {
            display: flex;
            gap: 12px;
            margin-bottom: 15px;
            max-width: 85%;
        }
        
        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
            margin-left: auto;
        }
        
        .message.assistant {
            align-self: flex-start;
        }
        
        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .message-content {
            flex: 1;
            padding: 16px 20px;
            border-radius: 16px;
            position: relative;
            line-height: 1.6;
        }
        
        /* 旧配色 */
        .old .message-avatar.user {
            background: #007AFF;
        }
        
        .old .message-content.user {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .old .message-avatar.assistant {
            background: #5856D6;
        }
        
        .old .message-content.assistant {
            background: #F2F2F7;
            color: #000000;
            border-bottom-left-radius: 4px;
        }
        
        /* 新配色 */
        .new .message-avatar.user {
            background: #1D4ED8;
        }
        
        .new .message-content.user {
            background: #E8F4FD;
            color: #1D4ED8;
            border: 1px solid #BFDBFE;
            border-bottom-right-radius: 4px;
        }
        
        .new .message-avatar.assistant {
            background: #64748B;
        }
        
        .new .message-content.assistant {
            background: #F8FAFC;
            color: #334155;
            border: 1px solid #E2E8F0;
            border-bottom-left-radius: 4px;
        }
        
        .time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 8px;
            text-align: right;
        }
        
        .improvement-note {
            background: #E8F5E8;
            border: 1px solid #90EE90;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .improvement-note h4 {
            color: #2D5A2D;
            margin: 0 0 10px 0;
        }
        
        .improvement-note ul {
            margin: 0;
            color: #2D5A2D;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="title">🎨 聊天界面配色优化对比</h1>
        
        <div class="comparison">
            <div class="section old">
                <h3>❌ 旧配色（不舒适）</h3>
                <div class="message user">
                    <div class="message-avatar user">👤</div>
                    <div class="message-content user">
                        你好
                        <div class="time">21:56:05</div>
                    </div>
                </div>
                <div class="message assistant">
                    <div class="message-avatar assistant">🤖</div>
                    <div class="message-content assistant">
                        您好！很高兴为您服务。请问有什么可以帮助您的吗？
                        <div class="time">21:56:07</div>
                    </div>
                </div>
                
                <p style="color: #d32f2f; font-size: 14px; margin-top: 15px;">
                    <strong>问题：</strong><br>
                    • 蓝色背景 + 白色文字对比度过强<br>
                    • 长时间阅读容易造成视觉疲劳<br>
                    • 缺乏层次感和边框定义
                </p>
            </div>
            
            <div class="section new">
                <h3>✅ 新配色（舒适）</h3>
                <div class="message user">
                    <div class="message-avatar user">👤</div>
                    <div class="message-content user">
                        你好
                        <div class="time">21:56:05</div>
                    </div>
                </div>
                <div class="message assistant">
                    <div class="message-avatar assistant">🤖</div>
                    <div class="message-content assistant">
                        您好！很高兴为您服务。请问有什么可以帮助您的吗？
                        <div class="time">21:56:07</div>
                    </div>
                </div>
                
                <p style="color: #2e7d32; font-size: 14px; margin-top: 15px;">
                    <strong>改进：</strong><br>
                    • 浅色背景 + 深色文字更易阅读<br>
                    • 柔和的色彩减少视觉疲劳<br>
                    • 清晰的边框增强层次感
                </p>
            </div>
        </div>
        
        <div class="improvement-note">
            <h4>🎯 配色改进要点</h4>
            <ul>
                <li><strong>用户消息</strong>：浅蓝色背景 (#E8F4FD) + 深蓝色文字 (#1D4ED8)</li>
                <li><strong>助手消息</strong>：浅灰色背景 (#F8FAFC) + 深灰色文字 (#334155)</li>
                <li><strong>边框定义</strong>：使用细边框增强消息边界感</li>
                <li><strong>色彩协调</strong>：头像颜色与消息内容颜色保持一致</li>
            </ul>
        </div>
    </div>
</body>
</html>