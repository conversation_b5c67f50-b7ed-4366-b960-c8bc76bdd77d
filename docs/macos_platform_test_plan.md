# macOS平台测试计划

**版本**: 1.0.0  
**日期**: 2025-04-25

## 测试目标

验证本地大模型防护系统在macOS平台上的安装、配置和运行情况，确保系统在macOS环境下能够正常工作，并与其他平台保持一致的功能和性能。

## 测试环境

### 硬件环境

- **处理器**: Apple M1/M2/Intel处理器
- **内存**: 8GB/16GB RAM
- **存储**: 至少20GB可用空间
- **网络**: 有线/无线网络连接

### 软件环境

- **操作系统**: macOS Monterey (12.0) / Ventura (13.0) / Sonoma (14.0)
- **Python版本**: Python 3.9 / 3.10 / 3.11
- **浏览器**: Safari / Chrome / Firefox
- **Docker**: Docker Desktop for Mac (如适用)
- **Ollama**: 最新版本

## 测试范围

1. **安装测试**
   - Python包安装
   - Docker容器安装
   - 可执行文件安装

2. **功能测试**
   - 核心功能
   - API功能
   - 用户界面功能

3. **性能测试**
   - 资源使用情况
   - 响应时间
   - 并发处理能力

4. **兼容性测试**
   - 不同macOS版本
   - 不同Python版本
   - 不同浏览器

5. **安全测试**
   - 权限验证
   - 数据安全

## 测试用例

### 1. 安装测试

#### 1.1 Python包安装

| 测试ID | TC-MACOS-INSTALL-001 |
|-------|---------------------|
| 测试名称 | macOS Python包安装测试 |
| 测试目的 | 验证系统可以通过pip在macOS上成功安装 |
| 前置条件 | 1. macOS系统已安装Python 3.9或更高版本<br>2. 已安装pip |
| 测试步骤 | 1. 创建虚拟环境: `python -m venv venv`<br>2. 激活虚拟环境: `source venv/bin/activate`<br>3. 安装包: `pip install -e .`<br>4. 验证安装: `python -c "import llm_protection; print(llm_protection.__version__)"` |
| 预期结果 | 1. 安装过程无错误<br>2. 验证命令输出正确的版本号 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 1.2 Docker容器安装

| 测试ID | TC-MACOS-INSTALL-002 |
|-------|---------------------|
| 测试名称 | macOS Docker容器安装测试 |
| 测试目的 | 验证系统可以通过Docker在macOS上成功运行 |
| 前置条件 | 1. macOS系统已安装Docker Desktop<br>2. Docker服务正在运行 |
| 测试步骤 | 1. 构建Docker镜像: `docker build -t llm-protection .`<br>2. 运行容器: `docker run -d -p 8080:8080 --name llm-protection-test llm-protection`<br>3. 检查容器状态: `docker ps`<br>4. 访问Web界面: 浏览器打开`http://localhost:8080` |
| 预期结果 | 1. 镜像构建成功<br>2. 容器启动成功<br>3. 容器状态为"运行中"<br>4. Web界面可以正常访问 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 1.3 可执行文件安装

| 测试ID | TC-MACOS-INSTALL-003 |
|-------|---------------------|
| 测试名称 | macOS可执行文件安装测试 |
| 测试目的 | 验证系统可以通过可执行文件在macOS上成功安装 |
| 前置条件 | 1. macOS系统<br>2. 已下载系统的macOS安装包(.dmg文件) |
| 测试步骤 | 1. 打开.dmg文件<br>2. 将应用程序拖到Applications文件夹<br>3. 从Applications文件夹或Launchpad启动应用程序<br>4. 如有安全提示，在"系统偏好设置 > 安全性与隐私"中允许运行 |
| 预期结果 | 1. 应用程序成功安装到Applications文件夹<br>2. 应用程序可以正常启动<br>3. 首次运行可能需要授权，授权后可以正常运行 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

### 2. 功能测试

#### 2.1 核心功能测试

| 测试ID | TC-MACOS-FUNC-001 |
|-------|------------------|
| 测试名称 | macOS核心功能测试 |
| 测试目的 | 验证系统的核心功能在macOS上正常工作 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 登录系统<br>2. 配置一个本地大模型连接(Ollama)<br>3. 创建/编辑安全规则<br>4. 使用聊天演示功能测试规则<br>5. 查看安全事件记录 |
| 预期结果 | 1. 登录成功<br>2. 模型连接配置成功<br>3. 规则创建/编辑成功<br>4. 聊天演示功能正常工作，规则能正确拦截<br>5. 安全事件正确记录 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 2.2 API功能测试

| 测试ID | TC-MACOS-FUNC-002 |
|-------|------------------|
| 测试名称 | macOS API功能测试 |
| 测试目的 | 验证系统的API功能在macOS上正常工作 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 使用curl测试健康检查API: `curl http://localhost:8080/api/v1/health`<br>2. 使用curl测试Ollama模型列表API: `curl http://localhost:8080/api/v1/ollama/models`<br>3. 使用curl测试代理API(需要认证) |
| 预期结果 | 1. 健康检查API返回200状态码和正确的JSON响应<br>2. Ollama模型列表API返回200状态码和正确的JSON响应<br>3. 代理API在认证后返回正确的响应 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 2.3 用户界面功能测试

| 测试ID | TC-MACOS-FUNC-003 |
|-------|------------------|
| 测试名称 | macOS用户界面功能测试 |
| 测试目的 | 验证系统的用户界面在macOS上正常工作 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 在Safari浏览器中访问系统<br>2. 在Chrome浏览器中访问系统<br>3. 在Firefox浏览器中访问系统<br>4. 测试所有主要页面的加载和交互<br>5. 测试响应式布局(调整浏览器窗口大小) |
| 预期结果 | 1. 在所有浏览器中界面正常显示<br>2. 所有页面正常加载<br>3. 所有交互功能正常工作<br>4. 响应式布局正常适应不同窗口大小 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

### 3. 性能测试

#### 3.1 资源使用测试

| 测试ID | TC-MACOS-PERF-001 |
|-------|------------------|
| 测试名称 | macOS资源使用测试 |
| 测试目的 | 验证系统在macOS上的资源使用情况 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 使用Activity Monitor监控系统运行时的CPU使用率<br>2. 使用Activity Monitor监控系统运行时的内存使用量<br>3. 使用Activity Monitor监控系统运行时的磁盘活动<br>4. 使用Activity Monitor监控系统运行时的网络活动<br>5. 在不同负载下(空闲、正常使用、高负载)记录资源使用情况 |
| 预期结果 | 1. CPU使用率在可接受范围内(空闲时<5%，正常使用时<30%，高负载时<70%)<br>2. 内存使用量在可接受范围内(不超过系统总内存的50%)<br>3. 磁盘和网络活动正常 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 3.2 响应时间测试

| 测试ID | TC-MACOS-PERF-002 |
|-------|------------------|
| 测试名称 | macOS响应时间测试 |
| 测试目的 | 验证系统在macOS上的响应时间 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 使用浏览器开发者工具测量页面加载时间<br>2. 使用curl测量API响应时间: `time curl http://localhost:8080/api/v1/health`<br>3. 测量聊天请求的响应时间<br>4. 在不同网络条件下测试响应时间 |
| 预期结果 | 1. 页面加载时间<2秒<br>2. API响应时间<200ms<br>3. 聊天请求响应时间与模型本身响应时间相近(额外开销<500ms) |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

### 4. 兼容性测试

#### 4.1 macOS版本兼容性测试

| 测试ID | TC-MACOS-COMP-001 |
|-------|------------------|
| 测试名称 | macOS版本兼容性测试 |
| 测试目的 | 验证系统在不同macOS版本上的兼容性 |
| 前置条件 | 1. 可访问不同版本的macOS系统(Monterey/Ventura/Sonoma) |
| 测试步骤 | 1. 在每个macOS版本上安装系统<br>2. 在每个macOS版本上执行基本功能测试<br>3. 记录任何版本特定的问题 |
| 预期结果 | 1. 系统在所有测试的macOS版本上都能成功安装<br>2. 基本功能在所有测试的macOS版本上都正常工作<br>3. 没有版本特定的严重问题 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

#### 4.2 Python版本兼容性测试

| 测试ID | TC-MACOS-COMP-002 |
|-------|------------------|
| 测试名称 | Python版本兼容性测试 |
| 测试目的 | 验证系统在不同Python版本上的兼容性 |
| 前置条件 | 1. macOS系统已安装多个Python版本(3.9/3.10/3.11) |
| 测试步骤 | 1. 使用每个Python版本创建虚拟环境<br>2. 在每个虚拟环境中安装系统<br>3. 在每个虚拟环境中执行基本功能测试<br>4. 记录任何版本特定的问题 |
| 预期结果 | 1. 系统在所有测试的Python版本上都能成功安装<br>2. 基本功能在所有测试的Python版本上都正常工作<br>3. 没有版本特定的严重问题 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

### 5. 安全测试

#### 5.1 权限验证测试

| 测试ID | TC-MACOS-SEC-001 |
|-------|-----------------|
| 测试名称 | macOS权限验证测试 |
| 测试目的 | 验证系统在macOS上的权限控制 |
| 前置条件 | 1. 系统已成功安装<br>2. 系统已启动并可访问 |
| 测试步骤 | 1. 尝试未认证访问需要认证的API<br>2. 尝试使用无效凭据登录<br>3. 尝试访问未授权的资源<br>4. 检查系统文件和目录的权限 |
| 预期结果 | 1. 未认证访问被拒绝<br>2. 无效凭据登录失败<br>3. 未授权资源访问被拒绝<br>4. 系统文件和目录具有适当的权限 |
| 实际结果 | |
| 状态 | 未执行 |
| 备注 | |

## 测试执行计划

| 阶段 | 测试用例 | 计划日期 | 负责人 |
|-----|---------|---------|-------|
| 安装测试 | TC-MACOS-INSTALL-001 ~ TC-MACOS-INSTALL-003 | 2025-04-25 | |
| 功能测试 | TC-MACOS-FUNC-001 ~ TC-MACOS-FUNC-003 | 2025-04-25 | |
| 性能测试 | TC-MACOS-PERF-001 ~ TC-MACOS-PERF-002 | 2025-04-25 | |
| 兼容性测试 | TC-MACOS-COMP-001 ~ TC-MACOS-COMP-002 | 2025-04-26 | |
| 安全测试 | TC-MACOS-SEC-001 | 2025-04-26 | |

## 测试环境准备

1. 确保测试机器满足最低硬件要求
2. 安装所需的软件环境(Python, Docker等)
3. 准备测试数据和测试工具
4. 确保网络连接正常

## 风险与缓解策略

| 风险 | 可能性 | 影响 | 缓解策略 |
|-----|-------|------|---------|
| macOS权限问题 | 中 | 高 | 确保应用程序有适当的权限；提供详细的权限设置指南 |
| Python依赖冲突 | 中 | 中 | 使用虚拟环境；明确指定依赖版本 |
| 不同macOS版本兼容性问题 | 低 | 高 | 在多个macOS版本上测试；提供版本特定的安装说明 |
| 资源使用过高 | 低 | 中 | 监控资源使用；优化性能；提供最低系统要求建议 |

## 测试报告模板

```
# macOS平台测试报告

**版本**: 1.0.0  
**日期**: 2025-04-XX
**测试人员**: 

## 测试摘要

- 测试用例总数: XX
- 通过: XX
- 失败: XX
- 阻塞: XX
- 未执行: XX

## 测试环境

- macOS版本: 
- 处理器: 
- 内存: 
- Python版本: 
- 浏览器: 

## 测试结果

| 测试ID | 测试名称 | 状态 | 备注 |
|-------|---------|------|------|
| TC-MACOS-INSTALL-001 | macOS Python包安装测试 | | |
| ... | ... | ... | ... |

## 发现的问题

| 问题ID | 问题描述 | 严重程度 | 状态 |
|-------|---------|---------|------|
| ISSUE-001 | | | |
| ... | ... | ... | ... |

## 结论与建议

(测试结论和改进建议)

```

## 附录

### 测试数据

- 测试用户凭据
- 测试模型配置
- 测试规则配置

### 测试工具

- curl
- Activity Monitor
- 浏览器开发者工具

---

本测试计划最后更新于2025年4月25日。
