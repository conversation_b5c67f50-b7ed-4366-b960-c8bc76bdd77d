# 本地大模型防护系统用户手册

**版本**: 1.0.0  
**更新日期**: 2025-04-25

## 目录

1. [简介](#简介)
2. [快速入门](#快速入门)
3. [用户界面](#用户界面)
   - [监控中心](#监控中心)
   - [规则管理](#规则管理)
   - [规则配置](#规则配置)
   - [安全事件](#安全事件)
   - [聊天演示](#聊天演示)
   - [模型管理](#模型管理)
   - [系统设置](#系统设置)
4. [安全规则](#安全规则)
   - [规则类型](#规则类型)
   - [规则优先级](#规则优先级)
   - [创建自定义规则](#创建自定义规则)
   - [规则模板](#规则模板)
5. [模型管理](#模型管理-1)
   - [查看模型列表](#查看模型列表)
   - [添加新模型](#添加新模型)
   - [删除模型](#删除模型)
   - [模型规则配置](#模型规则配置)
6. [安全事件处理](#安全事件处理)
   - [查看安全事件](#查看安全事件)
   - [事件分析](#事件分析)
   - [事件导出](#事件导出)
7. [系统监控](#系统监控)
   - [实时指标](#实时指标)
   - [性能监控](#性能监控)
   - [请求统计](#请求统计)
8. [高级功能](#高级功能)
   - [API集成](#api集成)
   - [自定义拦截器](#自定义拦截器)
   - [批量操作](#批量操作)
9. [故障排除](#故障排除)
10. [附录](#附录)

## 简介

本地大模型防护系统是一个专为保护本地部署的大型语言模型（LLM）设计的安全中间件。它能够检测和阻止各种潜在的安全威胁，如提示注入、敏感信息泄露、越狱尝试和有害内容生成等。系统提供了全面的安全防护功能，并通过直观的管理界面进行配置和监控。

### 主要功能

- **实时拦截与检测**: 拦截并分析所有发送到大模型的请求和响应
- **多种安全规则**: 内置多种安全规则，覆盖提示注入、越狱尝试、敏感信息和有害内容
- **模型特定配置**: 为不同的大语言模型配置不同的安全规则集
- **实时监控**: 监控系统性能和安全事件
- **事件记录与分析**: 记录并分析所有安全事件
- **用户友好界面**: 提供直观的Web界面进行配置和监控

## 快速入门

本节将帮助您快速上手使用本地大模型防护系统。

### 登录系统

1. 打开Web浏览器，访问`http://localhost:8080`（或您配置的其他地址）
2. 使用默认凭据登录（首次登录后请修改密码）：
   - 用户名: `admin`
   - 密码: `admin`

### 基本工作流程

1. **配置模型**: 在"模型管理"中添加或确认您的本地大模型
2. **配置规则**: 在"规则管理"中查看和调整安全规则
3. **分配规则**: 在"规则配置"中为每个模型分配适当的规则集
4. **测试防护**: 使用"聊天演示"功能测试防护效果
5. **监控事件**: 在"安全事件"中查看被拦截的请求

## 用户界面

本地大模型防护系统提供了直观的Web界面，包含以下主要部分：

### 监控中心

监控中心是系统的主页，提供系统状态的概览：

- **系统状态**: 显示系统的运行状态和关键指标
- **最近事件**: 显示最近的安全事件
- **资源使用**: 显示CPU、内存使用率等系统资源指标
- **请求统计**: 显示请求数量、拦截率等统计信息

![监控中心界面](images/dashboard.png)

### 规则管理

规则管理页面允许您查看、创建、编辑和删除安全规则：

- **规则列表**: 显示所有可用的安全规则
- **规则分类**: 按类型（提示注入、越狱尝试等）分类显示规则
- **规则详情**: 查看规则的详细信息和匹配条件
- **规则编辑**: 创建和编辑自定义规则

![规则管理界面](images/rules_management.png)

### 规则配置

规则配置页面允许您为不同的模型配置不同的规则集：

- **模型列表**: 显示所有已配置的模型
- **规则集分配**: 为每个模型分配规则集
- **规则集模板**: 使用预定义的规则集模板（高、中、低安全级别）
- **自定义配置**: 创建自定义规则集配置

![规则配置界面](images/rules_configuration.png)

### 安全事件

安全事件页面显示所有被检测到的安全事件：

- **事件列表**: 显示所有安全事件
- **事件过滤**: 按时间、类型、严重程度等过滤事件
- **事件详情**: 查看事件的详细信息，包括触发规则和原始内容
- **事件统计**: 显示事件统计信息和趋势

![安全事件界面](images/security_events.png)

### 聊天演示

聊天演示页面提供了一个简单的聊天界面，用于测试防护系统：

- **模型选择**: 选择要使用的大语言模型
- **聊天界面**: 与所选模型进行对话
- **实时防护**: 实时显示防护系统的拦截和警告
- **响应详情**: 查看模型响应的详细信息

![聊天演示界面](images/chat_demo.png)

### 模型管理

模型管理页面允许您管理连接到系统的大语言模型：

- **模型列表**: 显示所有可用的模型
- **模型详情**: 查看模型的详细信息
- **添加模型**: 添加新的模型连接
- **删除模型**: 删除不再使用的模型

![模型管理界面](images/model_management.png)

### 系统设置

系统设置页面允许您配置系统的各种参数：

- **基本设置**: 配置系统的基本参数，如端口、日志级别等
- **安全设置**: 配置系统的安全参数，如认证方式、会话超时等
- **高级设置**: 配置系统的高级参数，如性能调优、代理设置等
- **用户管理**: 管理系统用户和权限

![系统设置界面](images/system_settings.png)

## 安全规则

安全规则是本地大模型防护系统的核心，用于检测和阻止潜在的安全威胁。

### 规则类型

系统支持以下类型的安全规则：

1. **提示注入检测**: 检测试图操纵或绕过模型指令的提示
2. **越狱尝试检测**: 检测试图绕过模型安全限制的尝试
3. **敏感信息检测**: 检测包含敏感信息（如个人身份信息、信用卡号等）的内容
4. **有害内容检测**: 检测包含有害内容（如暴力、仇恨言论等）的内容
5. **内容合规性检测**: 检测不符合特定合规要求的内容

### 规则优先级

每条规则都有一个优先级，用于确定规则的执行顺序：

- **高优先级**: 最先执行，通常用于最严格的安全规则
- **中优先级**: 在高优先级规则之后执行
- **低优先级**: 最后执行，通常用于次要的安全规则

### 创建自定义规则

您可以通过以下步骤创建自定义规则：

1. 在"规则管理"页面，点击"创建规则"按钮
2. 填写规则基本信息：
   - 名称: 规则的唯一名称
   - 描述: 规则的详细描述
   - 类型: 规则类型（提示注入、越狱尝试等）
   - 优先级: 规则优先级（高、中、低）
3. 配置规则匹配条件：
   - 关键词匹配: 指定要匹配的关键词或短语
   - 正则表达式: 使用正则表达式进行复杂匹配
   - 模式匹配: 使用预定义的模式进行匹配
4. 配置规则操作：
   - 阻止: 阻止请求并返回错误
   - 警告: 允许请求但记录警告
   - 修改: 修改请求内容后允许
5. 点击"保存"按钮保存规则

### 规则模板

系统提供了以下预定义的规则模板：

- **高安全级别**: 包含所有类型的严格规则，适用于高安全需求场景
- **中安全级别**: 包含平衡的规则集，适用于一般场景
- **低安全级别**: 仅包含基本安全规则，适用于低安全需求场景
- **自定义**: 根据需要自定义规则集

## 模型管理

模型管理功能允许您管理连接到系统的大语言模型。

### 查看模型列表

在"模型管理"页面，您可以查看所有已配置的模型：

- 模型名称
- 模型类型（Ollama、OpenAI等）
- 模型状态（在线、离线）
- 已分配的规则集

### 添加新模型

要添加新模型，请按照以下步骤操作：

1. 在"模型管理"页面，点击"添加模型"按钮
2. 选择模型类型（Ollama、OpenAI等）
3. 填写模型信息：
   - 名称: 模型的唯一名称
   - 地址: 模型的API地址
   - 凭据: 访问模型所需的凭据（如API密钥）
4. 点击"保存"按钮保存模型

### 删除模型

要删除模型，请按照以下步骤操作：

1. 在"模型管理"页面，找到要删除的模型
2. 点击模型行右侧的"删除"按钮
3. 在确认对话框中点击"确认"

### 模型规则配置

要为模型配置规则集，请按照以下步骤操作：

1. 在"规则配置"页面，找到要配置的模型
2. 点击模型行右侧的"配置"按钮
3. 选择要使用的规则集模板（高、中、低安全级别）或创建自定义配置
4. 如果选择自定义配置，选择要包含的规则
5. 点击"保存"按钮保存配置

## 安全事件处理

安全事件处理功能允许您查看和分析被检测到的安全事件。

### 查看安全事件

在"安全事件"页面，您可以查看所有安全事件：

- 事件ID
- 时间戳
- 事件类型（提示注入、越狱尝试等）
- 严重程度（高、中、低）
- 触发规则
- 原始内容

### 事件分析

要分析事件，请按照以下步骤操作：

1. 在"安全事件"页面，找到要分析的事件
2. 点击事件行查看详细信息
3. 在详细信息页面，您可以查看：
   - 事件完整信息
   - 触发规则详情
   - 原始请求和响应
   - 匹配的内容和上下文

### 事件导出

要导出事件，请按照以下步骤操作：

1. 在"安全事件"页面，使用过滤器选择要导出的事件
2. 点击"导出"按钮
3. 选择导出格式（CSV、JSON等）
4. 点击"确认"按钮开始导出

## 系统监控

系统监控功能允许您监控系统的性能和状态。

### 实时指标

在"监控中心"页面，您可以查看以下实时指标：

- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络流量

### 性能监控

在"监控中心"的"性能"选项卡，您可以查看以下性能指标：

- 请求响应时间
- 请求处理速率
- 系统负载
- 资源使用趋势

### 请求统计

在"监控中心"的"统计"选项卡，您可以查看以下请求统计：

- 总请求数
- 拦截请求数
- 拦截率
- 按类型分类的拦截统计

## 高级功能

本地大模型防护系统提供了一些高级功能，适用于有特殊需求的用户。

### API集成

系统提供了RESTful API，允许您将防护功能集成到其他应用程序中：

- 请求拦截API
- 规则管理API
- 事件查询API
- 系统监控API

详细的API文档请参阅[API文档](api_documentation.md)。

### 自定义拦截器

高级用户可以开发自定义拦截器，以实现特定的安全需求：

1. 创建拦截器类，实现`Interceptor`接口
2. 实现`intercept`方法，处理请求和响应
3. 在配置文件中注册拦截器
4. 重启系统使拦截器生效

### 批量操作

系统支持批量操作，方便管理大量规则和事件：

- 批量导入规则
- 批量导出规则
- 批量删除事件
- 批量应用规则集

## 故障排除

如果您在使用系统时遇到问题，请参考以下常见问题及解决方案：

### 系统无法启动

**问题**: 系统无法正常启动。

**解决方案**:
- 检查日志文件（位于数据目录下的`logs`文件夹）
- 确认配置文件正确
- 确认端口未被占用
- 检查系统资源是否充足

### 无法连接到模型

**问题**: 系统报错无法连接到大语言模型。

**解决方案**:
- 确认模型服务正在运行
- 检查模型地址和凭据是否正确
- 确认网络连接正常
- 检查防火墙设置

### 规则不生效

**问题**: 配置的安全规则没有生效。

**解决方案**:
- 确认规则已正确配置并启用
- 检查规则优先级
- 确认规则已分配给相应的模型
- 检查规则匹配条件是否正确

### 性能问题

**问题**: 系统性能下降。

**解决方案**:
- 检查系统资源使用情况
- 减少同时运行的规则数量
- 优化规则匹配条件
- 增加系统资源（内存、CPU等）

## 附录

### 术语表

- **LLM**: 大型语言模型（Large Language Model）
- **提示注入**: 试图操纵或绕过模型指令的攻击
- **越狱尝试**: 试图绕过模型安全限制的攻击
- **规则集**: 一组安全规则的集合
- **拦截器**: 处理请求和响应的组件

### 快捷键

- `Ctrl+D`: 打开仪表盘
- `Ctrl+R`: 打开规则管理
- `Ctrl+E`: 打开安全事件
- `Ctrl+M`: 打开模型管理
- `Ctrl+S`: 打开系统设置

### 相关资源

- [官方网站](https://example.com)
- [GitHub仓库](https://github.com/yourusername/local-llm-protection-system)
- [API文档](api_documentation.md)
- [常见问题解答](faq.md)

---

本文档最后更新于2025年4月25日。请访问[官方文档](https://example.com/docs/user-manual)获取最新版本。
