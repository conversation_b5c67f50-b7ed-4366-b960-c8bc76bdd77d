<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地大模型防护系统 - 实时监控面板</title>
    <link rel="stylesheet" href="/static/admin/css/apple-main.css">
    <link rel="stylesheet" href="/static/admin/css/queue-table.css">
    <script src="/static/admin/js/apple-ui.js" defer></script>
    <!-- 引入 Chart.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 保留旧样式以防万一，但不使用 -->
    <link rel="stylesheet" href="css/admin.css" media="none">
</head>

<body>
    <div class="container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNoaWVsZCI+PHBhdGggZD0iTTEyIDIycy04LTUtOC0xMlY1bDgtM2w4IDN2N2MwIDctOCAxMi04IDEyeiIvPjwvc3ZnPg=="
                        alt="Logo">
                    <h2>本地大模型防护系统</h2>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="index.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-chart-line"></i></span>
                        <span class="sidebar-menu-text">监控中心</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-shield-alt"></i></span>
                        <span class="sidebar-menu-text">规则管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="model_rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-cogs"></i></span>
                        <span class="sidebar-menu-text">规则配置</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="events.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-exclamation-triangle"></i></span>
                        <span class="sidebar-menu-text">安全事件</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="monitor.html" class="sidebar-menu-link active">
                        <span class="sidebar-menu-icon"><i class="fas fa-desktop"></i></span>
                        <span class="sidebar-menu-text">实时监控</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="models.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-brain"></i></span>
                        <span class="sidebar-menu-text">模型管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/index.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-comment-dots"></i></span>
                        <span class="sidebar-menu-text">聊天演示</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="d-flex align-items-center justify-content-center gap-sm">
                    <span>暗色模式</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="dark-mode-toggle">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="mt-sm">版本 1.0.2</div>
            </div>
        </aside>

        <main class="main-content">
            <!-- 顶部导航栏 -->
            <nav class="top-navbar">
                <div class="navbar-left">
                    <button class="navbar-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="navbar-title">本地大模型防护系统</h1>
                </div>
                <div class="navbar-right">
                    <span id="current-time"></span>
                </div>
            </nav>

            <!-- 页面标题 -->
            <header class="page-header">
                <h1 class="page-title">实时监控面板</h1>
                <div class="page-actions">
                    <div class="refresh-controls">
                        <select id="refresh-interval" class="select-control">
                            <option value="0">手动刷新</option>
                            <option value="5" selected>5秒</option>
                            <option value="10">10秒</option>
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                        </select>
                        <button class="button" id="refresh-btn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </header>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="stat-card-content">
                        <h3 class="stat-card-title">CPU 使用率</h3>
                        <div class="stat-card-value" id="cpu-usage">--</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-memory"></i>
                    </div>
                    <div class="stat-card-content">
                        <h3 class="stat-card-title">内存使用率</h3>
                        <div class="stat-card-value" id="memory-usage">--</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="stat-card-content">
                        <h3 class="stat-card-title">活跃请求数</h3>
                        <div class="stat-card-value" id="active-requests">--</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-card-content">
                        <h3 class="stat-card-title">平均响应时间</h3>
                        <div class="stat-card-value" id="avg-response-time">--</div>
                    </div>
                </div>
            </div>

            <!-- 系统资源使用趋势 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-chart-area"></i>
                        系统资源使用趋势
                    </h2>
                    <div class="card-actions">
                        <select id="resource-time-range" class="select-control">
                            <option value="5">最近5分钟</option>
                            <option value="15" selected>最近15分钟</option>
                            <option value="30">最近30分钟</option>
                            <option value="60">最近1小时</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="resource-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 请求统计 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-chart-line"></i>
                        请求统计
                    </h2>
                    <div class="card-actions">
                        <select id="request-time-range" class="select-control">
                            <option value="5">最近5分钟</option>
                            <option value="15" selected>最近15分钟</option>
                            <option value="30">最近30分钟</option>
                            <option value="60">最近1小时</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="request-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 安全事件统计 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-shield-alt"></i>
                        安全事件统计
                    </h2>
                    <div class="card-actions">
                        <select id="event-time-range" class="select-control">
                            <option value="1">今天</option>
                            <option value="7" selected>最近7天</option>
                            <option value="30">最近30天</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="event-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 队列状态 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-tasks"></i>
                        队列状态
                    </h2>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th style="width: 20%;">队列名称</th>
                                    <th style="width: 15%; text-align: center;">等待任务数</th>
                                    <th style="width: 15%; text-align: center;">处理中任务数</th>
                                    <th style="width: 20%; text-align: center;">平均等待时间</th>
                                    <th style="width: 15%; text-align: center;">状态</th>
                                </tr>
                            </thead>
                            <tbody id="queue-table-body">
                                <tr>
                                    <td>高优先级队列</td>
                                    <td style="text-align: center;" id="high-queue-waiting">--</td>
                                    <td style="text-align: center;" id="high-queue-processing">--</td>
                                    <td style="text-align: center;" id="high-queue-wait-time">--</td>
                                    <td style="text-align: center;"><span class="badge success"
                                            id="high-queue-status">正常</span></td>
                                </tr>
                                <tr>
                                    <td>普通优先级队列</td>
                                    <td style="text-align: center;" id="normal-queue-waiting">--</td>
                                    <td style="text-align: center;" id="normal-queue-processing">--</td>
                                    <td style="text-align: center;" id="normal-queue-wait-time">--</td>
                                    <td style="text-align: center;"><span class="badge success"
                                            id="normal-queue-status">正常</span></td>
                                </tr>
                                <tr>
                                    <td>低优先级队列</td>
                                    <td style="text-align: center;" id="low-queue-waiting">--</td>
                                    <td style="text-align: center;" id="low-queue-processing">--</td>
                                    <td style="text-align: center;" id="low-queue-wait-time">--</td>
                                    <td style="text-align: center;"><span class="badge success"
                                            id="low-queue-status">正常</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 模型使用统计 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-brain"></i>
                        模型使用统计
                    </h2>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="model-usage-chart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 全局变量
            let resourceChart = null;
            let requestChart = null;
            let eventChart = null;
            let modelUsageChart = null;
            let refreshInterval = 5; // 默认5秒刷新一次
            let refreshTimer = null;

            // 检查并应用暗色模式设置
            const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const storedTheme = localStorage.getItem('theme');

            if (storedTheme === 'dark' || (!storedTheme && prefersDarkMode)) {
                document.body.classList.add('dark-mode');
                document.getElementById('dark-mode-toggle').checked = true;
            }

            // 初始化页面
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            initCharts();
            loadMonitorData();

            // 设置刷新定时器
            startRefreshTimer();

            // 绑定事件
            document.getElementById('refresh-btn').addEventListener('click', loadMonitorData);
            document.getElementById('refresh-interval').addEventListener('change', function () {
                refreshInterval = parseInt(this.value);
                startRefreshTimer();
            });

            document.getElementById('resource-time-range').addEventListener('change', function () {
                updateResourceChart();
            });

            document.getElementById('request-time-range').addEventListener('change', function () {
                updateRequestChart();
            });

            document.getElementById('event-time-range').addEventListener('change', function () {
                updateEventChart();
            });

            // 更新当前时间
            function updateCurrentTime() {
                const now = new Date();
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                document.getElementById('current-time').textContent = now.toLocaleDateString('zh-CN', options);
            }

            // 启动刷新定时器
            function startRefreshTimer() {
                // 清除现有定时器
                if (refreshTimer) {
                    clearInterval(refreshTimer);
                    refreshTimer = null;
                }

                // 如果选择了自动刷新，则设置定时器
                if (refreshInterval > 0) {
                    refreshTimer = setInterval(loadMonitorData, refreshInterval * 1000);
                }
            }

            // 初始化图表
            function initCharts() {
                // 检测是否为暗色模式
                const isDarkMode = document.body.classList.contains('dark-mode');

                // 设置图表样式
                Chart.defaults.color = isDarkMode ? '#AEAEB2' : '#636366';
                Chart.defaults.borderColor = isDarkMode ? '#3A3A3C' : '#E5E5EA';

                // 系统资源使用趋势图
                const resourceCtx = document.getElementById('resource-chart').getContext('2d');
                resourceChart = new Chart(resourceCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: 'CPU 使用率 (%)',
                                data: [],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                tension: 0.4
                            },
                            {
                                label: '内存使用率 (%)',
                                data: [],
                                borderColor: 'rgba(153, 102, 255, 1)',
                                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                grid: {
                                    color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    color: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                                }
                            }
                        }
                    }
                });

                // 请求统计图
                const requestCtx = document.getElementById('request-chart').getContext('2d');
                requestChart = new Chart(requestCtx, {
                    type: 'line',
                    data: {
                        labels: [],
                        datasets: [
                            {
                                label: '总请求数',
                                data: [],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                tension: 0.4
                            },
                            {
                                label: '成功请求数',
                                data: [],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                tension: 0.4
                            },
                            {
                                label: '阻止请求数',
                                data: [],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // 安全事件统计图
                const eventCtx = document.getElementById('event-chart').getContext('2d');
                eventChart = new Chart(eventCtx, {
                    type: 'bar',
                    data: {
                        labels: ['提示注入', '越狱尝试', '敏感信息', '有害内容', '合规违规'],
                        datasets: [
                            {
                                label: '安全事件数量',
                                data: [0, 0, 0, 0, 0],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.5)',
                                    'rgba(255, 159, 64, 0.5)',
                                    'rgba(255, 205, 86, 0.5)',
                                    'rgba(75, 192, 192, 0.5)',
                                    'rgba(54, 162, 235, 0.5)'
                                ],
                                borderColor: [
                                    'rgb(255, 99, 132)',
                                    'rgb(255, 159, 64)',
                                    'rgb(255, 205, 86)',
                                    'rgb(75, 192, 192)',
                                    'rgb(54, 162, 235)'
                                ],
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // 模型使用统计图
                const modelUsageCtx = document.getElementById('model-usage-chart').getContext('2d');
                modelUsageChart = new Chart(modelUsageCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Llama 2', 'Gemma 3', 'Qwen', 'DeepSeek', '其他'],
                        datasets: [
                            {
                                data: [0, 0, 0, 0, 0],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.5)',
                                    'rgba(54, 162, 235, 0.5)',
                                    'rgba(255, 206, 86, 0.5)',
                                    'rgba(75, 192, 192, 0.5)',
                                    'rgba(153, 102, 255, 0.5)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)',
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)'
                                ],
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            // 加载监控数据
            async function loadMonitorData() {
                try {
                    // 获取系统指标
                    const metricsResponse = await fetch('/api/v1/metrics');
                    if (metricsResponse.ok) {
                        const metricsData = await metricsResponse.json();
                        updateSystemMetrics(metricsData);
                    }

                    // 更新图表数据
                    updateResourceChart();
                    updateRequestChart();
                    updateEventChart();
                    updateModelUsageChart();

                    // 更新队列状态
                    updateQueueStatus();
                } catch (error) {
                    console.error('加载监控数据失败:', error);
                    showAlert('加载监控数据失败: ' + error.message, 'danger');
                }
            }

            // 显示提示消息
            function showAlert(message, type = 'info') {
                console.log(`[${type}] ${message}`);
                // 如果需要可以在这里添加显示提示消息的代码
            }

            // 监听暗色模式切换
            document.getElementById('dark-mode-toggle').addEventListener('change', function () {
                // 重新初始化图表以应用新的样式
                setTimeout(() => {
                    // 等待暗色模式类被添加到body后再初始化图表
                    initCharts();
                    // 重新加载数据
                    loadMonitorData();
                }, 100);
            });

            // 更新系统指标
            function updateSystemMetrics(data) {
                // 使用API返回的真实数据
                const cpuUsage = data.cpu_usage ? Math.round(data.cpu_usage) : 0;
                const memoryUsage = data.memory_usage ? Math.round(data.memory_usage) : 0;
                const activeRequests = data.active_requests || 0;
                const avgResponseTime = data.avg_response_time || 0;

                document.getElementById('cpu-usage').textContent = cpuUsage + '%';
                document.getElementById('memory-usage').textContent = memoryUsage + '%';
                document.getElementById('active-requests').textContent = activeRequests;
                document.getElementById('avg-response-time').textContent = Math.round(avgResponseTime) + 'ms';

                // 根据使用率设置颜色
                setCpuUsageColor(cpuUsage);
                setMemoryUsageColor(memoryUsage);

                console.log('系统指标已更新:', data);
            }

            // 设置CPU使用率颜色
            function setCpuUsageColor(usage) {
                const element = document.getElementById('cpu-usage');
                if (usage < 50) {
                    element.style.color = '#2ecc71'; // 绿色
                } else if (usage < 80) {
                    element.style.color = '#f39c12'; // 黄色
                } else {
                    element.style.color = '#e74c3c'; // 红色
                }
            }

            // 设置内存使用率颜色
            function setMemoryUsageColor(usage) {
                const element = document.getElementById('memory-usage');
                if (usage < 50) {
                    element.style.color = '#2ecc71'; // 绿色
                } else if (usage < 80) {
                    element.style.color = '#f39c12'; // 黄色
                } else {
                    element.style.color = '#e74c3c'; // 红色
                }
            }

            // 更新资源使用趋势图
            async function updateResourceChart() {
                const timeRange = parseInt(document.getElementById('resource-time-range').value);

                try {
                    // 从 API 获取真实资源使用数据
                    const response = await fetch(`/api/v1/metrics/resource?minutes=${timeRange}`);
                    if (!response.ok) {
                        throw new Error(`获取资源使用数据失败: ${response.status}`);
                    }

                    const resourceData = await response.json();
                    console.log('资源使用数据:', resourceData);

                    if (resourceData && resourceData.length > 0) {
                        // 提取时间标签和数据
                        const labels = [];
                        const cpuData = [];
                        const memoryData = [];

                        resourceData.forEach(item => {
                            // 将时间字符串转换为 Date 对象
                            const timestamp = new Date(item.timestamp);
                            labels.push(timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }));

                            cpuData.push(Math.round(item.cpu_usage));
                            memoryData.push(Math.round(item.memory_usage));
                        });

                        // 更新图表
                        resourceChart.data.labels = labels;
                        resourceChart.data.datasets[0].data = cpuData;
                        resourceChart.data.datasets[1].data = memoryData;
                        resourceChart.update();
                    }
                } catch (error) {
                    console.error('获取资源使用数据失败:', error);
                }
            }

            // 更新请求统计图
            async function updateRequestChart() {
                const timeRange = parseInt(document.getElementById('request-time-range').value);

                try {
                    // 从 API 获取真实请求统计数据
                    const response = await fetch(`/api/v1/metrics/requests?minutes=${timeRange}`);
                    if (!response.ok) {
                        throw new Error(`获取请求统计数据失败: ${response.status}`);
                    }

                    const requestData = await response.json();
                    console.log('请求统计数据:', requestData);

                    if (requestData && requestData.length > 0) {
                        // 提取时间标签和数据
                        const labels = [];
                        const totalData = [];
                        const successData = [];
                        const blockedData = [];

                        requestData.forEach(item => {
                            // 将时间字符串转换为 Date 对象
                            const timestamp = new Date(item.timestamp);
                            labels.push(timestamp.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' }));

                            totalData.push(item.total_requests);
                            successData.push(item.success_requests);
                            blockedData.push(item.blocked_requests);
                        });

                        // 更新图表
                        requestChart.data.labels = labels;
                        requestChart.data.datasets[0].data = totalData;
                        requestChart.data.datasets[1].data = successData;
                        requestChart.data.datasets[2].data = blockedData;
                        requestChart.update();
                    }
                } catch (error) {
                    console.error('获取请求统计数据失败:', error);
                }
            }

            // 更新安全事件统计图
            async function updateEventChart() {
                const timeRange = parseInt(document.getElementById('event-time-range').value);

                try {
                    // 从 API 获取真实安全事件统计数据
                    const response = await fetch(`/api/v1/metrics/events?days=${timeRange}`);
                    if (!response.ok) {
                        throw new Error(`获取安全事件统计数据失败: ${response.status}`);
                    }

                    const eventsData = await response.json();
                    console.log('安全事件统计数据:', eventsData);

                    if (eventsData && eventsData.length > 0) {
                        // 将所有天的数据累加起来
                        let promptInjection = 0;
                        let jailbreak = 0;
                        let sensitiveInfo = 0;
                        let harmfulContent = 0;
                        let complianceViolation = 0;

                        eventsData.forEach(item => {
                            promptInjection += item.prompt_injection || 0;
                            jailbreak += item.jailbreak || 0;
                            sensitiveInfo += item.sensitive_info || 0;
                            harmfulContent += item.harmful_content || 0;
                            complianceViolation += item.compliance_violation || 0;
                        });

                        // 更新图表
                        eventChart.data.datasets[0].data = [
                            promptInjection,
                            jailbreak,
                            sensitiveInfo,
                            harmfulContent,
                            complianceViolation
                        ];
                        eventChart.update();
                    }
                } catch (error) {
                    console.error('获取安全事件统计数据失败:', error);
                }
            }

            // 更新模型使用统计图
            async function updateModelUsageChart() {
                try {
                    // 从 API 获取真实模型使用统计数据
                    const response = await fetch('/api/v1/metrics/models');
                    if (!response.ok) {
                        throw new Error(`获取模型使用统计数据失败: ${response.status}`);
                    }

                    const modelsData = await response.json();
                    console.log('模型使用统计数据:', modelsData);

                    if (modelsData && modelsData.length > 0) {
                        // 提取模型名称和请求数
                        const modelNames = [];
                        const requestCounts = [];

                        // 最多显示前5个模型
                        const topModels = modelsData.slice(0, 5);

                        topModels.forEach(item => {
                            modelNames.push(item.model_name);
                            requestCounts.push(item.request_count);
                        });

                        // 更新图表标签和数据
                        modelUsageChart.data.labels = modelNames;
                        modelUsageChart.data.datasets[0].data = requestCounts;
                        modelUsageChart.update();
                    } else {
                        // 如果没有数据，显示默认模型
                        modelUsageChart.data.labels = ['Llama 2', 'Gemma 3', 'Qwen', 'DeepSeek', '其他'];
                        modelUsageChart.data.datasets[0].data = [0, 0, 0, 0, 0];
                        modelUsageChart.update();
                    }
                } catch (error) {
                    console.error('获取模型使用统计数据失败:', error);
                    // 出错时显示默认模型
                    modelUsageChart.data.labels = ['Llama 2', 'Gemma 3', 'Qwen', 'DeepSeek', '其他'];
                    modelUsageChart.data.datasets[0].data = [0, 0, 0, 0, 0];
                    modelUsageChart.update();
                }
            }

            // 更新队列状态
            async function updateQueueStatus() {
                try {
                    // 从 API 获取真实队列状态数据
                    const response = await fetch('/api/v1/metrics/queues');
                    if (!response.ok) {
                        throw new Error(`获取队列状态数据失败: ${response.status}`);
                    }

                    const queuesData = await response.json();
                    console.log('队列状态数据:', queuesData);

                    if (queuesData && queuesData.length > 0) {
                        // 遍历队列数据并更新界面
                        queuesData.forEach(queue => {
                            if (queue.name === '高优先级队列') {
                                document.getElementById('high-queue-waiting').textContent = queue.waiting_tasks;
                                document.getElementById('high-queue-processing').textContent = queue.processing_tasks;
                                document.getElementById('high-queue-wait-time').textContent = Math.round(queue.avg_wait_time) + 'ms';

                                // 设置状态标签
                                const statusElement = document.getElementById('high-queue-status');
                                statusElement.className = `badge ${getStatusClass(queue.status)}`;
                                statusElement.textContent = queue.status;
                            } else if (queue.name === '普通优先级队列') {
                                document.getElementById('normal-queue-waiting').textContent = queue.waiting_tasks;
                                document.getElementById('normal-queue-processing').textContent = queue.processing_tasks;
                                document.getElementById('normal-queue-wait-time').textContent = Math.round(queue.avg_wait_time) + 'ms';

                                // 设置状态标签
                                const statusElement = document.getElementById('normal-queue-status');
                                statusElement.className = `badge ${getStatusClass(queue.status)}`;
                                statusElement.textContent = queue.status;
                            } else if (queue.name === '低优先级队列') {
                                document.getElementById('low-queue-waiting').textContent = queue.waiting_tasks;
                                document.getElementById('low-queue-processing').textContent = queue.processing_tasks;
                                document.getElementById('low-queue-wait-time').textContent = Math.round(queue.avg_wait_time) + 'ms';

                                // 设置状态标签
                                const statusElement = document.getElementById('low-queue-status');
                                statusElement.className = `badge ${getStatusClass(queue.status)}`;
                                statusElement.textContent = queue.status;
                            }
                        });
                    } else {
                        // 如果没有数据，使用默认值
                        setDefaultQueueStatus();
                    }
                } catch (error) {
                    console.error('获取队列状态数据失败:', error);
                    // 出错时使用默认值
                    setDefaultQueueStatus();
                }
            }

            // 根据状态文本获取状态类名
            function getStatusClass(status) {
                switch (status) {
                    case '正常':
                        return 'success';
                    case '繁忙':
                        return 'warning';
                    case '拥堵':
                        return 'danger';
                    default:
                        return 'success';
                }
            }

            // 设置默认队列状态
            function setDefaultQueueStatus() {
                // 高优先级队列
                document.getElementById('high-queue-waiting').textContent = '0';
                document.getElementById('high-queue-processing').textContent = '0';
                document.getElementById('high-queue-wait-time').textContent = '0ms';
                document.getElementById('high-queue-status').className = 'badge success';
                document.getElementById('high-queue-status').textContent = '正常';

                // 普通优先级队列
                document.getElementById('normal-queue-waiting').textContent = '0';
                document.getElementById('normal-queue-processing').textContent = '0';
                document.getElementById('normal-queue-wait-time').textContent = '0ms';
                document.getElementById('normal-queue-status').className = 'badge success';
                document.getElementById('normal-queue-status').textContent = '正常';

                // 低优先级队列
                document.getElementById('low-queue-waiting').textContent = '0';
                document.getElementById('low-queue-processing').textContent = '0';
                document.getElementById('low-queue-wait-time').textContent = '0ms';
                document.getElementById('low-queue-status').className = 'badge success';
                document.getElementById('low-queue-status').textContent = '正常';
            }

            // 设置队列状态
            function setQueueStatus(elementId, waitingCount) {
                const element = document.getElementById(elementId);
                if (waitingCount < 10) {
                    element.className = 'badge success';
                    element.textContent = '正常';
                } else if (waitingCount < 30) {
                    element.className = 'badge warning';
                    element.textContent = '繁忙';
                } else {
                    element.className = 'badge danger';
                    element.textContent = '拥堵';
                }
            }
        });
    </script>
</body>

</html>