/*
 * 苹果风格的大模型防火墙界面过滤器样式
 * 统一下拉框和过滤器的样式
 */

/* ===== 过滤器容器 ===== */
.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--apple-spacing-md);
  margin-bottom: var(--apple-spacing-lg);
  padding: var(--apple-spacing-lg);
  background-color: var(--apple-bg-secondary);
  border-radius: var(--apple-border-radius);
  align-items: flex-end;
}

/* 横向过滤器网格 */
.filters-grid-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: var(--apple-spacing-md);
  width: 100%;
  align-items: flex-end;
}

.filters-grid-horizontal .filter-item {
  flex: 1;
  min-width: 150px;
  margin-bottom: 0;
}

.filters-grid-horizontal .search-item {
  flex: 2;
  min-width: 250px;
}

/* ===== 统一下拉框样式 ===== */
.filter-dropdown {
  position: relative;
  width: 200px;
  /* 统一宽度 */
  margin-bottom: 0;
}

.filter-dropdown select {
  width: 100%;
  height: 44px;
  /* 统一高度 */
  padding: 10px 16px;
  padding-right: 40px;
  /* 为下拉箭头留出空间 */
  font-size: var(--apple-font-size-md);
  color: var(--apple-text-primary);
  background-color: var(--apple-bg-primary);
  border: 1px solid var(--apple-gray-3);
  border-radius: 8px;
  appearance: none;
  /* 移除默认样式 */
  cursor: pointer;
  transition: border-color var(--apple-transition-normal), box-shadow var(--apple-transition-normal);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%238E8E93' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
}

.filter-dropdown select:focus {
  outline: none;
  border-color: var(--apple-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.filter-dropdown label {
  display: block;
  margin-bottom: var(--apple-spacing-xs);
  font-size: var(--apple-font-size-sm);
  color: var(--apple-text-secondary);
  font-weight: 500;
}

/* ===== 搜索框样式 ===== */
.search-filter {
  position: relative;
  flex: 1;
  min-width: 200px;
}

.search-filter input {
  width: 100%;
  height: 44px;
  /* 与下拉框高度一致 */
  padding: 10px 16px 10px 40px;
  /* 为搜索图标留出空间 */
  font-size: var(--apple-font-size-md);
  color: var(--apple-text-primary);
  background-color: var(--apple-bg-primary);
  border: 1px solid var(--apple-gray-3);
  border-radius: 8px;
  transition: border-color var(--apple-transition-normal), box-shadow var(--apple-transition-normal);
}

.search-filter input:focus {
  outline: none;
  border-color: var(--apple-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-filter .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--apple-gray-5);
  pointer-events: none;
  /* 确保图标不会干扰点击 */
}

.search-filter label {
  display: block;
  margin-bottom: var(--apple-spacing-xs);
  font-size: var(--apple-font-size-sm);
  color: var(--apple-text-secondary);
  font-weight: 500;
}

/* ===== 过滤器按钮 ===== */
.filter-actions {
  display: flex;
  gap: var(--apple-spacing-sm);
}

.filter-button {
  height: 44px;
  /* 与下拉框高度一致 */
  padding: 0 16px;
  font-size: var(--apple-font-size-md);
  border-radius: 8px;
  background-color: var(--apple-primary);
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color var(--apple-transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-button:hover {
  background-color: #0062cc;
}

.filter-button.secondary {
  background-color: var(--apple-bg-tertiary);
  color: var(--apple-text-primary);
}

.filter-button.white {
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
}

.filter-button.secondary:hover {
  background-color: var(--apple-gray-3);
}

/* ===== 响应式设计 ===== */
@media (max-width: 992px) {
  .filter-dropdown {
    width: calc(50% - var(--apple-spacing-md) / 2);
  }

  .search-filter {
    width: 100%;
    margin-top: var(--apple-spacing-md);
  }
}

@media (max-width: 768px) {
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-dropdown {
    width: 100%;
  }

  .filter-actions {
    margin-top: var(--apple-spacing-md);
  }

  .filter-button {
    flex: 1;
  }
}