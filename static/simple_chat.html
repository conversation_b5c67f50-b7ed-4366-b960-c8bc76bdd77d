<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>简化聊天测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .message {
            margin: 10px 0;
            padding: 15px;
            border-radius: 12px;
            max-width: 80%;
        }
        .message.user {
            background: #E8F4FD;
            color: #1D4ED8;
            border: 1px solid #BFDBFE;
            margin-left: auto;
        }
        .message.assistant {
            background: #F8FAFC;
            color: #334155;
            border: 1px solid #E2E8F0;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: #1D4ED8;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1E40AF;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
        }
        .status.success { background: #e8f5e8; color: #2d5a2d; }
        .status.error { background: #fee; color: #d32f2f; }
        .status.info { background: #e3f2fd; color: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 简化聊天测试</h1>
        
        <div id="messages"></div>
        
        <div class="input-area">
            <input type="text" id="message-input" placeholder="输入消息..." maxlength="500">
            <button id="send-button" onclick="sendMessage()">发送</button>
        </div>
        
        <div id="status"></div>
    </div>

    <script>
        let isGenerating = false;

        function addMessage(role, content) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.textContent = content;
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        async function sendMessage() {
            const input = document.getElementById('message-input');
            const button = document.getElementById('send-button');
            const message = input.value.trim();
            
            if (!message || isGenerating) return;
            
            isGenerating = true;
            button.disabled = true;
            button.textContent = '发送中...';
            
            addMessage('user', message);
            input.value = '';
            
            setStatus('正在发送消息...', 'info');
            
            try {
                console.log('开始发送请求...');
                
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer cherry-studio-key'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:latest',
                        messages: [{
                            role: 'user',
                            content: message
                        }],
                        stream: false
                    })
                });
                
                console.log('收到响应，状态:', response.status);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                const data = await response.json();
                console.log('解析响应数据:', data);
                
                const assistantMessage = data.choices[0]?.message?.content || '无法获取响应内容';
                addMessage('assistant', assistantMessage);
                setStatus('消息发送成功！', 'success');
                
            } catch (error) {
                console.error('发送失败:', error);
                addMessage('assistant', `错误: ${error.message}`);
                setStatus(`发送失败: ${error.message}`, 'error');
            } finally {
                isGenerating = false;
                button.disabled = false;
                button.textContent = '发送';
            }
        }

        // 支持回车发送
        document.getElementById('message-input').addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        });

        // 页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            setStatus('页面加载完成，可以开始聊天！', 'success');
        });
    </script>
</body>
</html>