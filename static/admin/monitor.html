<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>监控中心 - 本地大模型防护系统</title>
    <link rel="stylesheet" href="/static/admin/css/apple-main.css">
    <script src="/static/admin/js/apple-ui.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 监控中心专用样式 */
        :root {
            --primary-color: #007AFF;
            --secondary-color: #5856D6;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --danger-color: #FF3B30;
            --info-color: #5AC8FA;
            --surface-color: #FFFFFF;
            --surface-secondary: #F2F2F7;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --border-color: #C6C6C8;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
        }

        /* 监控状态指示器 */
        .monitor-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator.online { background: var(--success-color); }
        .status-indicator.warning { background: var(--warning-color); }
        .status-indicator.offline { background: var(--danger-color); }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-text {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .status-time {
            font-size: 14px;
            color: var(--text-secondary);
            margin-left: auto;
        }

        /* 增强的监控卡片 */
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .monitor-card {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            padding: 24px;
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            position: relative;
            overflow: hidden;
        }

        .monitor-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .monitor-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .monitor-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .monitor-card-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            position: relative;
        }

        .monitor-card-icon.system { background: linear-gradient(135deg, var(--info-color), #30B0FF); }
        .monitor-card-icon.security { background: linear-gradient(135deg, var(--success-color), #30D158); }
        .monitor-card-icon.performance { background: linear-gradient(135deg, var(--warning-color), #FFCC02); }
        .monitor-card-icon.alerts { background: linear-gradient(135deg, var(--danger-color), #FF453A); }

        .monitor-card-content {
            flex: 1;
            margin-left: 16px;
        }

        .monitor-card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 4px 0;
        }

        .monitor-card-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 8px 0;
            display: flex;
            align-items: baseline;
            gap: 8px;
        }

        .monitor-card-unit {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .monitor-card-change {
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .monitor-card-change.positive { color: var(--success-color); }
        .monitor-card-change.negative { color: var(--danger-color); }
        .monitor-card-change.neutral { color: var(--text-secondary); }

        .monitor-card-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
        }

        /* 专业监控面板 */
        .monitoring-panel {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            margin-bottom: 24px;
            overflow: hidden;
        }

        .panel-header {
            background: linear-gradient(135deg, var(--surface-secondary), #E5E5EA);
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .panel-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .panel-title-icon {
            width: 32px;
            height: 32px;
            border-radius: var(--radius-sm);
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .panel-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .panel-body {
            padding: 24px;
        }

        /* 实时指标网格 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric-item {
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            padding: 16px;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .metric-item:hover {
            background: var(--surface-color);
            box-shadow: var(--shadow-sm);
        }

        .metric-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .metric-trend {
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        /* 图表容器增强 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 16px 0;
        }

        .chart-container.large {
            height: 400px;
        }

        /* 活动日志 */
        .activity-log {
            max-height: 400px;
            overflow-y: auto;
        }

        .log-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            flex-shrink: 0;
        }

        .log-icon.info { background: var(--info-color); }
        .log-icon.warning { background: var(--warning-color); }
        .log-icon.error { background: var(--danger-color); }
        .log-icon.success { background: var(--success-color); }

        .log-content {
            flex: 1;
        }

        .log-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 4px 0;
        }

        .log-description {
            font-size: 13px;
            color: var(--text-secondary);
            margin: 0 0 4px 0;
        }

        .log-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 系统拓扑图 */
        .topology-container {
            position: relative;
            height: 400px;
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            padding: 20px;
            overflow: hidden;
        }

        .topology-svg {
            width: 100%;
            height: 100%;
        }

        .topology-node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .topology-node:hover {
            transform: scale(1.1);
        }

        .topology-node circle {
            transition: all 0.3s ease;
        }

        .topology-node.online circle {
            fill: var(--success-color);
            stroke: var(--success-color);
        }

        .topology-node.warning circle {
            fill: var(--warning-color);
            stroke: var(--warning-color);
        }

        .topology-node.offline circle {
            fill: var(--danger-color);
            stroke: var(--danger-color);
        }

        .topology-node.unknown circle {
            fill: var(--text-secondary);
            stroke: var(--text-secondary);
        }

        .topology-connection {
            stroke-width: 3;
            transition: all 0.3s ease;
        }

        .topology-connection.active {
            stroke: var(--success-color);
            stroke-dasharray: 5,5;
            animation: connectionFlow 2s linear infinite;
        }

        .topology-connection.error {
            stroke: var(--danger-color);
            stroke-dasharray: 10,5;
        }

        .topology-connection.inactive {
            stroke: var(--border-color);
            stroke-dasharray: 2,2;
        }

        @keyframes connectionFlow {
            0% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: 10; }
        }

        .topology-label {
            font-size: 12px;
            font-weight: 600;
            fill: var(--text-primary);
            text-anchor: middle;
            dominant-baseline: middle;
        }

        .topology-metrics {
            font-size: 10px;
            fill: var(--text-secondary);
            text-anchor: middle;
        }

        .topology-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .topology-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-top: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: var(--radius-sm);
        }

        .topology-stat {
            text-align: center;
        }

        .topology-stat-value {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .topology-stat-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .monitor-grid {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .panel-header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .panel-controls {
                justify-content: center;
            }
        }

        /* 暗色模式支持 */
        .dark-mode {
            --surface-color: #1C1C1E;
            --surface-secondary: #2C2C2E;
            --text-primary: #FFFFFF;
            --text-secondary: #8E8E93;
            --border-color: #3A3A3C;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNoaWVsZCI+PHBhdGggZD0iTTEyIDIycy04LTUtOC0xMlY1bDgtM2w4IDN2N2MwIDctOCAxMi04IDEyeiIvPjwvc3ZnPg=="
                        alt="Logo">
                    <h2>本地大模型防护系统</h2>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="monitor.html" class="sidebar-menu-link active">
                        <span class="sidebar-menu-icon"><i class="fas fa-desktop"></i></span>
                        <span class="sidebar-menu-text">监控中心</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-shield-alt"></i></span>
                        <span class="sidebar-menu-text">规则管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="model_rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-cogs"></i></span>
                        <span class="sidebar-menu-text">规则配置</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="events.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-exclamation-triangle"></i></span>
                        <span class="sidebar-menu-text">安全事件</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="models.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-brain"></i></span>
                        <span class="sidebar-menu-text">模型管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/index.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-comment-dots"></i></span>
                        <span class="sidebar-menu-text">模型测试</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="d-flex align-items-center justify-content-center gap-sm">
                    <span>暗色模式</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="dark-mode-toggle">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="mt-sm">版本 1.0.3</div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <nav class="top-navbar">
                <div class="navbar-left">
                    <button class="navbar-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="navbar-title">本地大模型防护系统</h1>
                </div>
                <div class="navbar-right">
                    <span id="current-time"></span>
                </div>
            </nav>

            <!-- 页面标题 -->
            <header class="page-header">
                <h1 class="page-title">监控中心</h1>
                <div class="page-actions">
                    <div class="refresh-controls">
                        <select id="refresh-interval" class="select-control">
                            <option value="0">手动刷新</option>
                            <option value="5" selected>5秒</option>
                            <option value="10">10秒</option>
                            <option value="30">30秒</option>
                            <option value="60">1分钟</option>
                        </select>
                        <button class="button" id="refresh-btn">
                            <i class="fas fa-sync-alt"></i> 刷新数据
                        </button>
                    </div>
                </div>
            </header>

            <!-- 系统状态指示器 -->
            <div class="monitor-status">
                <div class="status-indicator online" id="system-status"></div>
                <span class="status-text" id="status-text">系统运行正常</span>
                <span class="status-time" id="uptime">运行时间: 00:00:00</span>
            </div>

            <!-- 核心监控指标 -->
            <div class="monitor-grid">
                <div class="monitor-card">
                    <div class="monitor-card-header">
                        <div class="monitor-card-icon system">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="monitor-card-content">
                            <h3 class="monitor-card-title">系统性能</h3>
                            <div class="monitor-card-value">
                                <span id="cpu-usage">--</span>
                                <span class="monitor-card-unit">%</span>
                            </div>
                            <div class="monitor-card-change neutral" id="cpu-trend">
                                <i class="fas fa-minus"></i> 无变化
                            </div>
                        </div>
                    </div>
                    <p class="monitor-card-subtitle">CPU 使用率</p>
                </div>

                <div class="monitor-card">
                    <div class="monitor-card-header">
                        <div class="monitor-card-icon security">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="monitor-card-content">
                            <h3 class="monitor-card-title">安全防护</h3>
                            <div class="monitor-card-value">
                                <span id="blocked-requests">--</span>
                                <span class="monitor-card-unit">次</span>
                            </div>
                            <div class="monitor-card-change positive" id="security-trend">
                                <i class="fas fa-arrow-up"></i> 今日拦截
                            </div>
                        </div>
                    </div>
                    <p class="monitor-card-subtitle">威胁拦截数量</p>
                </div>

                <div class="monitor-card">
                    <div class="monitor-card-header">
                        <div class="monitor-card-icon performance">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="monitor-card-content">
                            <h3 class="monitor-card-title">响应性能</h3>
                            <div class="monitor-card-value">
                                <span id="avg-response-time">--</span>
                                <span class="monitor-card-unit">ms</span>
                            </div>
                            <div class="monitor-card-change neutral" id="performance-trend">
                                <i class="fas fa-minus"></i> 平均响应时间
                            </div>
                        </div>
                    </div>
                    <p class="monitor-card-subtitle">平均响应时间</p>
                </div>

                <div class="monitor-card">
                    <div class="monitor-card-header">
                        <div class="monitor-card-icon alerts">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="monitor-card-content">
                            <h3 class="monitor-card-title">活跃告警</h3>
                            <div class="monitor-card-value">
                                <span id="active-alerts">--</span>
                                <span class="monitor-card-unit">个</span>
                            </div>
                            <div class="monitor-card-change negative" id="alerts-trend">
                                <i class="fas fa-exclamation-circle"></i> 需要关注
                            </div>
                        </div>
                    </div>
                    <p class="monitor-card-subtitle">待处理告警</p>
                </div>
            </div>

            <!-- 实时指标面板 -->
            <div class="monitoring-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <div class="panel-title-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        实时监控指标
                    </h2>
                    <div class="panel-controls">
                        <select id="metrics-time-range" class="select-control">
                            <option value="5">最近5分钟</option>
                            <option value="15" selected>最近15分钟</option>
                            <option value="30">最近30分钟</option>
                            <option value="60">最近1小时</option>
                        </select>
                        <button class="button button-secondary" id="export-metrics">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-label">内存使用</div>
                            <div class="metric-value" id="memory-usage">--</div>
                            <div class="metric-trend neutral">
                                <i class="fas fa-minus"></i> 稳定
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">活跃连接</div>
                            <div class="metric-value" id="active-connections">--</div>
                            <div class="metric-trend positive">
                                <i class="fas fa-arrow-up"></i> +12%
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">队列长度</div>
                            <div class="metric-value" id="queue-length">--</div>
                            <div class="metric-trend negative">
                                <i class="fas fa-arrow-down"></i> -5%
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-label">错误率</div>
                            <div class="metric-value" id="error-rate">--</div>
                            <div class="metric-trend positive">
                                <i class="fas fa-arrow-down"></i> 降低
                            </div>
                        </div>
                    </div>
                    <div class="chart-container large">
                        <canvas id="realtime-metrics-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 系统架构拓扑 -->
            <div class="monitoring-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <div class="panel-title-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        系统架构拓扑
                    </h2>
                    <div class="panel-controls">
                        <button class="button button-secondary" id="refresh-topology">
                            <i class="fas fa-sync-alt"></i> 刷新拓扑
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="topology-container">
                        <svg class="topology-svg" id="topology-svg">
                            <!-- SVG content will be dynamically generated -->
                        </svg>
                        <div class="topology-tooltip" id="topology-tooltip"></div>
                    </div>
                    <div class="topology-stats" id="topology-stats">
                        <div class="topology-stat">
                            <div class="topology-stat-value" id="total-requests-stat">--</div>
                            <div class="topology-stat-label">总请求数</div>
                        </div>
                        <div class="topology-stat">
                            <div class="topology-stat-value" id="blocked-requests-stat">--</div>
                            <div class="topology-stat-label">拦截请求</div>
                        </div>
                        <div class="topology-stat">
                            <div class="topology-stat-value" id="pass-rate-stat">--</div>
                            <div class="topology-stat-label">通过率</div>
                        </div>
                        <div class="topology-stat">
                            <div class="topology-stat-value" id="avg-processing-time-stat">--</div>
                            <div class="topology-stat-label">平均处理时间</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 双栏布局：图表 + 活动日志 -->
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px;">
                <!-- 安全事件趋势 -->
                <div class="monitoring-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">
                            <div class="panel-title-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            安全事件趋势
                        </h2>
                        <div class="panel-controls">
                            <select id="events-time-range" class="select-control">
                                <option value="1">今天</option>
                                <option value="7" selected>最近7天</option>
                                <option value="30">最近30天</option>
                            </select>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="chart-container">
                            <canvas id="security-events-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 实时活动日志 -->
                <div class="monitoring-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">
                            <div class="panel-title-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            实时活动
                        </h2>
                        <div class="panel-controls">
                            <button class="button button-secondary" id="clear-log">
                                <i class="fas fa-eraser"></i> 清除
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="activity-log" id="activity-log">
                            <!-- 活动日志项目将动态加载 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型使用统计 -->
            <div class="monitoring-panel">
                <div class="panel-header">
                    <h2 class="panel-title">
                        <div class="panel-title-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        模型使用统计
                    </h2>
                    <div class="panel-controls">
                        <button class="button button-secondary" id="model-details">
                            <i class="fas fa-info-circle"></i> 详细统计
                        </button>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="chart-container">
                        <canvas id="model-usage-chart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 全局变量
        let refreshInterval = 5;
        let refreshTimer = null;
        let realtimeChart = null;
        let securityEventsChart = null;
        let modelUsageChart = null;
        let systemStartTime = Date.now(); // 模拟系统启动时间
        let topologyData = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeMonitoringCenter();
            setupEventListeners();
            startAutoRefresh();
        });

        function initializeMonitoringCenter() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            setInterval(updateUptime, 1000);
            
            initializeCharts();
            initializeTopology();
            loadMonitoringData();
            generateActivityLog();
        }

        function setupEventListeners() {
            // 刷新按钮
            document.getElementById('refresh-btn').addEventListener('click', loadMonitoringData);
            
            // 刷新间隔选择
            document.getElementById('refresh-interval').addEventListener('change', function() {
                refreshInterval = parseInt(this.value);
                startAutoRefresh();
            });

            // 时间范围选择器
            document.getElementById('metrics-time-range').addEventListener('change', updateRealtimeChart);
            document.getElementById('events-time-range').addEventListener('change', updateSecurityEventsChart);

            // 其他控制按钮
            document.getElementById('export-metrics').addEventListener('click', exportMetrics);
            document.getElementById('refresh-topology').addEventListener('click', refreshTopology);
            document.getElementById('clear-log').addEventListener('click', clearActivityLog);
            document.getElementById('model-details').addEventListener('click', showModelDetails);
        }

        function startAutoRefresh() {
            if (refreshTimer) {
                clearInterval(refreshTimer);
            }
            
            if (refreshInterval > 0) {
                refreshTimer = setInterval(loadMonitoringData, refreshInterval * 1000);
            }
        }

        function updateCurrentTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };
            document.getElementById('current-time').textContent = now.toLocaleDateString('zh-CN', options);
        }

        function updateUptime() {
            const uptime = Date.now() - systemStartTime;
            const hours = Math.floor(uptime / (1000 * 60 * 60));
            const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
            
            document.getElementById('uptime').textContent = 
                `运行时间: ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        async function loadMonitoringData() {
            try {
                // 加载系统指标
                const metricsResponse = await fetch('/api/v1/metrics');
                if (metricsResponse.ok) {
                    const data = await metricsResponse.json();
                    updateSystemMetrics(data);
                }

                // 更新图表
                updateRealtimeChart();
                updateSecurityEventsChart();
                updateModelUsageChart();
                updateTopology();
                updateSystemStatus();
                
            } catch (error) {
                console.error('加载监控数据失败:', error);
                updateSystemStatus('error');
            }
        }

        function updateSystemMetrics(data) {
            // 更新核心指标
            const cpuUsage = data.cpu_usage || 0;
            const memoryUsage = data.memory_usage || 0;
            const avgResponseTime = data.avg_response_time || 0;
            const activeRequests = data.active_requests || 0;

            document.getElementById('cpu-usage').textContent = Math.round(cpuUsage);
            document.getElementById('memory-usage').textContent = Math.round(memoryUsage) + '%';
            document.getElementById('avg-response-time').textContent = Math.round(avgResponseTime);
            document.getElementById('active-connections').textContent = activeRequests;

            // 模拟其他指标
            document.getElementById('blocked-requests').textContent = Math.floor(Math.random() * 50) + 10;
            document.getElementById('active-alerts').textContent = Math.floor(Math.random() * 5);
            document.getElementById('queue-length').textContent = Math.floor(Math.random() * 20);
            document.getElementById('error-rate').textContent = (Math.random() * 2).toFixed(2) + '%';

            // 更新趋势指示器
            updateTrendIndicators(cpuUsage, memoryUsage);
        }

        function updateTrendIndicators(cpuUsage, memoryUsage) {
            const cpuTrend = document.getElementById('cpu-trend');
            const securityTrend = document.getElementById('security-trend');

            // CPU趋势
            if (cpuUsage > 80) {
                cpuTrend.className = 'monitor-card-change negative';
                cpuTrend.innerHTML = '<i class="fas fa-arrow-up"></i> 高负载';
            } else if (cpuUsage < 30) {
                cpuTrend.className = 'monitor-card-change positive';
                cpuTrend.innerHTML = '<i class="fas fa-arrow-down"></i> 负载正常';
            } else {
                cpuTrend.className = 'monitor-card-change neutral';
                cpuTrend.innerHTML = '<i class="fas fa-minus"></i> 稳定运行';
            }
        }

        function updateSystemStatus(status = 'online') {
            const indicator = document.getElementById('system-status');
            const statusText = document.getElementById('status-text');

            switch (status) {
                case 'online':
                    indicator.className = 'status-indicator online';
                    statusText.textContent = '系统运行正常';
                    break;
                case 'warning':
                    indicator.className = 'status-indicator warning';
                    statusText.textContent = '系统负载较高';
                    break;
                case 'error':
                    indicator.className = 'status-indicator offline';
                    statusText.textContent = '系统异常，请检查';
                    break;
            }
        }

        function initializeCharts() {
            const isDarkMode = document.body.classList.contains('dark-mode');
            
            // 设置Chart.js默认样式
            Chart.defaults.color = isDarkMode ? '#AEAEB2' : '#636366';
            Chart.defaults.borderColor = isDarkMode ? '#3A3A3C' : '#E5E5EA';

            // 实时指标图表
            const realtimeCtx = document.getElementById('realtime-metrics-chart').getContext('2d');
            realtimeChart = new Chart(realtimeCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: 'CPU使用率 (%)',
                            data: [],
                            borderColor: '#007AFF',
                            backgroundColor: 'rgba(0, 122, 255, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '内存使用率 (%)',
                            data: [],
                            borderColor: '#5AC8FA',
                            backgroundColor: 'rgba(90, 200, 250, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '响应时间 (ms)',
                            data: [],
                            borderColor: '#FF9500',
                            backgroundColor: 'rgba(255, 149, 0, 0.1)',
                            tension: 0.4,
                            fill: true,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            max: 100,
                            title: {
                                display: true,
                                text: '使用率 (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '响应时间 (ms)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });

            // 安全事件图表
            const securityCtx = document.getElementById('security-events-chart').getContext('2d');
            securityEventsChart = new Chart(securityCtx, {
                type: 'bar',
                data: {
                    labels: ['提示注入', '越狱尝试', '敏感信息', '有害内容', '合规违规'],
                    datasets: [{
                        label: '事件数量',
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 159, 64, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(54, 162, 235, 0.8)'
                        ],
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 模型使用图表
            const modelCtx = document.getElementById('model-usage-chart').getContext('2d');
            modelUsageChart = new Chart(modelCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#007AFF',
                            '#5AC8FA',
                            '#34C759',
                            '#FF9500',
                            '#FF3B30',
                            '#5856D6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        async function updateRealtimeChart() {
            const timeRange = parseInt(document.getElementById('metrics-time-range').value);
            
            try {
                const response = await fetch(`/api/v1/metrics/resource?minutes=${timeRange}`);
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data && data.length > 0) {
                        const labels = [];
                        const cpuData = [];
                        const memoryData = [];
                        const responseData = [];

                        data.forEach(item => {
                            const time = new Date(item.timestamp);
                            labels.push(time.toLocaleTimeString('zh-CN', { 
                                hour: '2-digit', 
                                minute: '2-digit' 
                            }));
                            cpuData.push(Math.round(item.cpu_usage));
                            memoryData.push(Math.round(item.memory_usage));
                            responseData.push(Math.round(Math.random() * 200 + 50)); // 模拟响应时间
                        });

                        realtimeChart.data.labels = labels;
                        realtimeChart.data.datasets[0].data = cpuData;
                        realtimeChart.data.datasets[1].data = memoryData;
                        realtimeChart.data.datasets[2].data = responseData;
                        realtimeChart.update('none');
                    }
                }
            } catch (error) {
                console.error('更新实时图表失败:', error);
            }
        }

        async function updateSecurityEventsChart() {
            const timeRange = parseInt(document.getElementById('events-time-range').value);
            
            try {
                const response = await fetch(`/api/v1/metrics/events?days=${timeRange}`);
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data && data.length > 0) {
                        const eventCounts = [0, 0, 0, 0, 0];
                        
                        data.forEach(item => {
                            eventCounts[0] += item.prompt_injection || 0;
                            eventCounts[1] += item.jailbreak || 0;
                            eventCounts[2] += item.sensitive_info || 0;
                            eventCounts[3] += item.harmful_content || 0;
                            eventCounts[4] += item.compliance_violation || 0;
                        });

                        securityEventsChart.data.datasets[0].data = eventCounts;
                        securityEventsChart.update('none');
                    }
                }
            } catch (error) {
                console.error('更新安全事件图表失败:', error);
            }
        }

        async function updateModelUsageChart() {
            try {
                const response = await fetch('/api/v1/metrics/models');
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data && data.length > 0) {
                        const labels = data.slice(0, 6).map(item => item.model_name);
                        const usageData = data.slice(0, 6).map(item => item.request_count);

                        modelUsageChart.data.labels = labels;
                        modelUsageChart.data.datasets[0].data = usageData;
                        modelUsageChart.update('none');
                    }
                }
            } catch (error) {
                console.error('更新模型使用图表失败:', error);
            }
        }

        function generateActivityLog() {
            const logContainer = document.getElementById('activity-log');
            
            // 模拟实时活动日志
            const activities = [
                { type: 'info', title: '新用户连接', description: '用户 ************* 已连接到系统', time: '刚刚' },
                { type: 'warning', title: '高CPU使用率', description: 'CPU使用率达到 85%，建议检查系统负载', time: '2分钟前' },
                { type: 'success', title: '威胁拦截成功', description: '成功拦截一次提示注入攻击', time: '5分钟前' },
                { type: 'error', title: '模型连接失败', description: '无法连接到模型 llama2:7b', time: '8分钟前' },
                { type: 'info', title: '配置更新', description: '安全规则配置已更新', time: '10分钟前' },
                { type: 'success', title: '系统备份完成', description: '定时备份任务执行成功', time: '15分钟前' }
            ];

            logContainer.innerHTML = activities.map(activity => `
                <div class="log-item">
                    <div class="log-icon ${activity.type}">
                        <i class="fas fa-${getLogIcon(activity.type)}"></i>
                    </div>
                    <div class="log-content">
                        <div class="log-title">${activity.title}</div>
                        <div class="log-description">${activity.description}</div>
                        <div class="log-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        function getLogIcon(type) {
            const icons = {
                info: 'info-circle',
                warning: 'exclamation-triangle',
                error: 'times-circle',
                success: 'check-circle'
            };
            return icons[type] || 'info-circle';
        }

        // 控制按钮功能
        function exportMetrics() {
            alert('导出监控指标功能开发中...');
        }

        // Topology visualization functions
        function initializeTopology() {
            const svg = document.getElementById('topology-svg');
            const tooltip = document.getElementById('topology-tooltip');
            
            // Set up event listeners for tooltip
            svg.addEventListener('mouseover', handleTopologyMouseOver);
            svg.addEventListener('mouseout', handleTopologyMouseOut);
            svg.addEventListener('mousemove', handleTopologyMouseMove);
            
            // Initial topology render
            updateTopology();
        }
        
        async function updateTopology() {
            try {
                const response = await fetch('/api/v1/topology');
                if (response.ok) {
                    topologyData = await response.json();
                    renderTopology(topologyData);
                    updateTopologyStats(topologyData.flow_stats);
                } else {
                    console.error('Failed to load topology data');
                }
            } catch (error) {
                console.error('Error loading topology data:', error);
            }
        }
        
        function renderTopology(data) {
            const svg = document.getElementById('topology-svg');
            const svgRect = svg.getBoundingClientRect();
            const width = svgRect.width || 600;
            const height = svgRect.height || 300;
            
            // Clear existing content
            svg.innerHTML = '';
            
            // Create SVG definitions for arrow markers
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarker.setAttribute('id', 'arrowhead');
            arrowMarker.setAttribute('markerWidth', '10');
            arrowMarker.setAttribute('markerHeight', '7');
            arrowMarker.setAttribute('refX', '9');
            arrowMarker.setAttribute('refY', '3.5');
            arrowMarker.setAttribute('orient', 'auto');
            
            const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPath.setAttribute('points', '0 0, 10 3.5, 0 7');
            arrowPath.setAttribute('fill', '#34C759');
            
            arrowMarker.appendChild(arrowPath);
            defs.appendChild(arrowMarker);
            svg.appendChild(defs);
            
            // Render connections first (so they appear behind nodes)
            data.connections.forEach(connection => {
                renderConnection(svg, connection, data.nodes, width, height);
            });
            
            // Render nodes
            data.nodes.forEach(node => {
                renderNode(svg, node, width, height);
            });
        }
        
        function renderNode(svg, node, width, height) {
            const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            nodeGroup.setAttribute('class', `topology-node ${node.status}`);
            nodeGroup.setAttribute('data-node-id', node.id);
            
            // Calculate node position (center it in the available space)
            const centerY = height / 2;
            let x;
            if (node.id === 'client') {
                x = width * 0.2;
            } else if (node.id === 'security') {
                x = width * 0.5;
            } else if (node.id === 'llm_service') {
                x = width * 0.8;
            }
            
            // Node circle
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', x);
            circle.setAttribute('cy', centerY);
            circle.setAttribute('r', '30');
            circle.setAttribute('stroke-width', '3');
            
            // Node icon (using text for now, could be improved with actual icons)
            const icon = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            icon.setAttribute('x', x);
            icon.setAttribute('y', centerY);
            icon.setAttribute('text-anchor', 'middle');
            icon.setAttribute('dominant-baseline', 'central');
            icon.setAttribute('font-size', '20');
            icon.setAttribute('fill', 'white');
            icon.setAttribute('font-family', 'FontAwesome');
            
            let iconText = '';
            switch (node.type) {
                case 'client':
                    iconText = '👤';
                    break;
                case 'security':
                    iconText = '🛡️';
                    break;
                case 'llm':
                    iconText = '🧠';
                    break;
            }
            icon.textContent = iconText;
            
            // Node label
            const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            label.setAttribute('x', x);
            label.setAttribute('y', centerY + 50);
            label.setAttribute('class', 'topology-label');
            label.textContent = node.name;
            
            // Node metrics (show key metric below label)
            const metrics = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            metrics.setAttribute('x', x);
            metrics.setAttribute('y', centerY + 65);
            metrics.setAttribute('class', 'topology-metrics');
            
            let metricText = '';
            if (node.metrics) {
                switch (node.type) {
                    case 'client':
                        metricText = `${node.metrics.total_requests || 0} 请求`;
                        break;
                    case 'security':
                        metricText = `${node.metrics.blocked_requests || 0} 拦截`;
                        break;
                    case 'llm':
                        metricText = `${node.metrics.response_time || 0}ms`;
                        break;
                }
            }
            metrics.textContent = metricText;
            
            nodeGroup.appendChild(circle);
            nodeGroup.appendChild(icon);
            nodeGroup.appendChild(label);
            nodeGroup.appendChild(metrics);
            
            svg.appendChild(nodeGroup);
        }
        
        function renderConnection(svg, connection, nodes, width, height) {
            const sourceNode = nodes.find(n => n.id === connection.source);
            const targetNode = nodes.find(n => n.id === connection.target);
            
            if (!sourceNode || !targetNode) return;
            
            const centerY = height / 2;
            let sourceX, targetX;
            
            // Calculate positions
            if (sourceNode.id === 'client') sourceX = width * 0.2;
            else if (sourceNode.id === 'security') sourceX = width * 0.5;
            else if (sourceNode.id === 'llm_service') sourceX = width * 0.8;
            
            if (targetNode.id === 'client') targetX = width * 0.2;
            else if (targetNode.id === 'security') targetX = width * 0.5;
            else if (targetNode.id === 'llm_service') targetX = width * 0.8;
            
            // Adjust for node radius
            const adjustedSourceX = sourceX + 30;
            const adjustedTargetX = targetX - 30;
            
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', adjustedSourceX);
            line.setAttribute('y1', centerY);
            line.setAttribute('x2', adjustedTargetX);
            line.setAttribute('y2', centerY);
            line.setAttribute('class', `topology-connection ${connection.status}`);
            line.setAttribute('marker-end', 'url(#arrowhead)');
            line.setAttribute('data-connection', `${connection.source}-${connection.target}`);
            
            svg.appendChild(line);
        }
        
        function updateTopologyStats(flowStats) {
            document.getElementById('total-requests-stat').textContent = flowStats.total_requests || 0;
            document.getElementById('blocked-requests-stat').textContent = flowStats.blocked_requests || 0;
            document.getElementById('pass-rate-stat').textContent = `${100 - (flowStats.block_rate || 0)}%`;
            document.getElementById('avg-processing-time-stat').textContent = `${flowStats.avg_processing_time || 0}ms`;
        }
        
        function handleTopologyMouseOver(event) {
            const nodeElement = event.target.closest('.topology-node');
            const connectionElement = event.target.closest('.topology-connection');
            const tooltip = document.getElementById('topology-tooltip');
            
            if (nodeElement && topologyData) {
                const nodeId = nodeElement.getAttribute('data-node-id');
                const node = topologyData.nodes.find(n => n.id === nodeId);
                
                if (node) {
                    let tooltipContent = `<strong>${node.name}</strong><br>`;
                    tooltipContent += `状态: ${getStatusText(node.status)}<br>`;
                    
                    if (node.metrics) {
                        Object.entries(node.metrics).forEach(([key, value]) => {
                            const label = getMetricLabel(key);
                            tooltipContent += `${label}: ${value}<br>`;
                        });
                    }
                    
                    tooltip.innerHTML = tooltipContent;
                    tooltip.style.opacity = '1';
                }
            } else if (connectionElement && topologyData) {
                const connectionData = connectionElement.getAttribute('data-connection');
                const connection = topologyData.connections.find(c => `${c.source}-${c.target}` === connectionData);
                
                if (connection) {
                    let tooltipContent = `<strong>连接: ${connection.source} → ${connection.target}</strong><br>`;
                    tooltipContent += `状态: ${getStatusText(connection.status)}<br>`;
                    
                    if (connection.metrics) {
                        Object.entries(connection.metrics).forEach(([key, value]) => {
                            const label = getMetricLabel(key);
                            tooltipContent += `${label}: ${value}<br>`;
                        });
                    }
                    
                    tooltip.innerHTML = tooltipContent;
                    tooltip.style.opacity = '1';
                }
            }
        }
        
        function handleTopologyMouseOut(event) {
            const tooltip = document.getElementById('topology-tooltip');
            tooltip.style.opacity = '0';
        }
        
        function handleTopologyMouseMove(event) {
            const tooltip = document.getElementById('topology-tooltip');
            tooltip.style.left = (event.clientX + 10) + 'px';
            tooltip.style.top = (event.clientY - 10) + 'px';
        }
        
        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'warning': '警告',
                'error': '错误',
                'active': '活跃',
                'inactive': '非活跃',
                'unknown': '未知'
            };
            return statusMap[status] || status;
        }
        
        function getMetricLabel(key) {
            const labelMap = {
                'active_connections': '活跃连接',
                'total_requests': '总请求',
                'avg_response_time': '平均响应时间',
                'blocked_requests': '拦截请求',
                'detection_rate': '检测率',
                'rules_active': '活跃规则',
                'cpu_usage': 'CPU使用率',
                'memory_usage': '内存使用率',
                'model_count': '模型数量',
                'response_time': '响应时间',
                'queue_size': '队列长度',
                'disk_usage': '磁盘使用率',
                'throughput': '吞吐量',
                'latency': '延迟',
                'error_rate': '错误率'
            };
            return labelMap[key] || key;
        }

        function refreshTopology() {
            updateTopology();
        }

        function clearActivityLog() {
            document.getElementById('activity-log').innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 40px;">活动日志已清空</div>';
        }

        function showModelDetails() {
            alert('显示详细模型统计...');
        }

        // 自动生成新的活动日志项目
        setInterval(() => {
            const activities = [
                { type: 'info', title: '新请求处理', description: '处理了一个模型推理请求', time: '刚刚' },
                { type: 'success', title: '安全检查通过', description: '请求通过了所有安全检查', time: '刚刚' },
                { type: 'warning', title: '队列积压', description: '请求队列长度超过阈值', time: '刚刚' }
            ];

            const randomActivity = activities[Math.floor(Math.random() * activities.length)];
            const logContainer = document.getElementById('activity-log');
            
            const newLogItem = document.createElement('div');
            newLogItem.className = 'log-item';
            newLogItem.innerHTML = `
                <div class="log-icon ${randomActivity.type}">
                    <i class="fas fa-${getLogIcon(randomActivity.type)}"></i>
                </div>
                <div class="log-content">
                    <div class="log-title">${randomActivity.title}</div>
                    <div class="log-description">${randomActivity.description}</div>
                    <div class="log-time">${randomActivity.time}</div>
                </div>
            `;

            if (logContainer.children.length > 10) {
                logContainer.removeChild(logContainer.lastChild);
            }
            
            logContainer.insertBefore(newLogItem, logContainer.firstChild);
        }, 30000); // 每30秒添加一个新活动
    </script>
</body>

</html>