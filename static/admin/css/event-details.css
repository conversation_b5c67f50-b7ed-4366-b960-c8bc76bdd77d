/* 事件详情模态框样式 */
.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.detail-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.detail-value {
    font-size: 1rem;
    color: #333;
    word-break: break-word;
    line-height: 1.4;
}

.detail-section {
    margin-bottom: 30px;
    padding: 0 5px;
}

.detail-section-title {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    letter-spacing: 0.5px;
}

.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.9rem;
    color: #333;
    line-height: 1.5;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.code-inline {
    padding: 10px;
    max-height: 100px;
    margin-top: 5px;
}

.mt-sm {
    margin-top: 12px;
}

.mt-md {
    margin-top: 24px;
}

/* 模态框样式调整 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1050;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.modal-dialog.modal-lg {
    max-width: 800px;
    width: 90%;
    transform: translateY(20px);
    opacity: 0;
    transition: transform 0.3s, opacity 0.3s;
}

.modal-backdrop.show .modal-dialog {
    transform: translateY(0);
    opacity: 1;
}

.modal-content {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border: none;
    overflow: hidden;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #95a5a6;
    transition: color 0.2s;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.modal-close:hover {
    color: #2c3e50;
    background-color: #f8f9fa;
}

.modal-body {
    padding: 25px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 苹果风格样式 */
@media (prefers-color-scheme: dark) {
    .detail-grid {
        background-color: #2C2C2E;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .detail-item {
        background-color: #1C1C1E;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .detail-label {
        color: #8E8E93;
    }

    .detail-value {
        color: #FFFFFF;
    }

    .detail-section-title {
        color: #FFFFFF;
        border-bottom-color: #3A3A3C;
    }

    .code-block {
        background-color: #2C2C2E;
        border-color: #3A3A3C;
        color: #FFFFFF;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    .modal-content {
        background-color: #1C1C1E;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .modal-header,
    .modal-footer {
        border-color: #3A3A3C;
    }

    .modal-title {
        color: #FFFFFF;
    }

    .modal-close {
        color: #8E8E93;
    }

    .modal-close:hover {
        color: #FFFFFF;
        background-color: #2C2C2E;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
        padding: 15px;
        gap: 15px;
    }

    .detail-item {
        margin-bottom: 0;
    }

    .modal-dialog.modal-lg {
        width: 95%;
        margin: 10px auto;
    }

    .modal-body {
        padding: 15px;
    }
}