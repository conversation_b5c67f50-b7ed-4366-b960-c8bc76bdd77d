/* 
 * 苹果风格的大模型防火墙仪表盘页面
 * 仪表盘特定的样式
 */

/* ===== 仪表盘布局 ===== */
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: var(--apple-spacing-xl);
}

/* ===== 统计卡片网格 ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--apple-spacing-lg);
}

/* ===== 图表卡片 ===== */
.chart-card {
  background-color: var(--apple-bg-primary);
  border-radius: var(--apple-border-radius);
  box-shadow: var(--apple-shadow-sm);
  padding: var(--apple-spacing-lg);
  margin-bottom: var(--apple-spacing-lg);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--apple-spacing-lg);
}

.chart-title {
  font-size: var(--apple-font-size-md);
  font-weight: 600;
  color: var(--apple-text-primary);
  margin: 0;
}

.chart-actions {
  display: flex;
  gap: var(--apple-spacing-sm);
}

.chart-container {
  height: 300px;
  position: relative;
}

/* ===== 最近事件列表 ===== */
.recent-events {
  margin-bottom: var(--apple-spacing-xl);
}

.event-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.event-item {
  display: flex;
  align-items: flex-start;
  padding: var(--apple-spacing-md);
  border-bottom: 1px solid var(--apple-gray-2);
  transition: background-color var(--apple-transition-normal);
}

.event-item:last-child {
  border-bottom: none;
}

.event-item:hover {
  background-color: var(--apple-gray-1);
}

.event-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--apple-spacing-md);
  flex-shrink: 0;
}

.event-icon.success {
  background-color: rgba(52, 199, 89, 0.1);
  color: var(--apple-success);
}

.event-icon.warning {
  background-color: rgba(255, 149, 0, 0.1);
  color: var(--apple-warning);
}

.event-icon.danger {
  background-color: rgba(255, 59, 48, 0.1);
  color: var(--apple-danger);
}

.event-icon.info {
  background-color: rgba(90, 200, 250, 0.1);
  color: var(--apple-info);
}

.event-content {
  flex: 1;
}

.event-title {
  font-weight: 600;
  margin-bottom: var(--apple-spacing-xs);
  color: var(--apple-text-primary);
}

.event-description {
  color: var(--apple-text-secondary);
  font-size: var(--apple-font-size-sm);
  margin-bottom: var(--apple-spacing-xs);
}

.event-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--apple-font-size-xs);
  color: var(--apple-text-tertiary);
}

.event-time {
  display: flex;
  align-items: center;
  gap: var(--apple-spacing-xs);
}

/* ===== 功能模块卡片 ===== */
.feature-modules {
  margin-bottom: var(--apple-spacing-xl);
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--apple-spacing-lg);
}

.module-card {
  background-color: var(--apple-bg-primary);
  border-radius: var(--apple-border-radius);
  box-shadow: var(--apple-shadow-sm);
  padding: var(--apple-spacing-lg);
  transition: transform var(--apple-transition-normal), box-shadow var(--apple-transition-normal);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.module-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--apple-shadow-md);
}

.module-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--apple-spacing-md);
  background-color: var(--apple-primary);
  color: white;
}

.module-title {
  font-size: var(--apple-font-size-md);
  font-weight: 600;
  margin-bottom: var(--apple-spacing-sm);
  color: var(--apple-text-primary);
}

.module-description {
  color: var(--apple-text-secondary);
  font-size: var(--apple-font-size-sm);
  margin-bottom: var(--apple-spacing-lg);
  flex: 1;
}

.module-action {
  margin-top: auto;
}

/* ===== 系统状态卡片 ===== */
.system-status {
  background-color: var(--apple-bg-primary);
  border-radius: var(--apple-border-radius);
  box-shadow: var(--apple-shadow-sm);
  padding: var(--apple-spacing-lg);
  margin-bottom: var(--apple-spacing-xl);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--apple-spacing-lg);
}

.status-title {
  font-size: var(--apple-font-size-md);
  font-weight: 600;
  color: var(--apple-text-primary);
  margin: 0;
}

.status-refresh {
  background: none;
  border: none;
  color: var(--apple-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--apple-spacing-xs);
  font-size: var(--apple-font-size-sm);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--apple-spacing-md);
}

.status-item {
  display: flex;
  flex-direction: column;
  padding: var(--apple-spacing-md);
  background-color: var(--apple-bg-secondary);
  border-radius: 8px;
}

.status-label {
  font-size: var(--apple-font-size-xs);
  color: var(--apple-text-secondary);
  margin-bottom: var(--apple-spacing-xs);
}

.status-value {
  font-size: var(--apple-font-size-md);
  font-weight: 600;
  color: var(--apple-text-primary);
  display: flex;
  align-items: center;
  gap: var(--apple-spacing-xs);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.healthy {
  background-color: var(--apple-success);
}

.status-indicator.warning {
  background-color: var(--apple-warning);
}

.status-indicator.critical {
  background-color: var(--apple-danger);
}

/* ===== 响应式设计 ===== */
@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-card {
    overflow-x: auto;
  }
  
  .chart-container {
    min-width: 500px;
  }
}
