/* 模型规则配置页面样式 */

/* 页面布局优化 */
.main-content {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.header {
    margin-bottom: 30px;
}

.card {
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
    padding-bottom: 15px;
    margin-bottom: 20px;
}

/* 模型信息面板 */
.model-info-panel {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.model-info-item {
    display: flex;
    flex-direction: column;
    min-width: 150px;
    background-color: white;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.model-info-item .label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.model-info-item .value {
    font-size: 1rem;
    font-weight: 600;
    color: #343a40;
}

/* 配置操作按钮 */
.config-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.config-actions .button {
    min-width: 120px;
    text-align: center;
    padding: 10px 15px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.config-actions .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 规则过滤器 */
.rules-filter {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    justify-content: center;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.rules-filter input,
.rules-filter select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    flex: 1;
    min-width: 180px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.rules-filter input:focus,
.rules-filter select:focus {
    border-color: #3498db;
    box-shadow: 0 1px 3px rgba(52, 152, 219, 0.3);
    outline: none;
}

/* 模型选择列表 */
.model-selection-list {
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.model-selection-item {
    display: flex;
    align-items: center;
    padding: 12px 10px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.model-selection-item:hover {
    background-color: #f8f9fa;
}

.model-selection-item:last-child {
    border-bottom: none;
}

.model-selection-item label {
    margin-left: 10px;
    flex: 1;
    cursor: pointer;
}

/* 安全评分样式 */
.security-score {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: bold;
    text-align: center;
    min-width: 50px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.security-score.high {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.security-score.medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.security-score.low {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 规则状态开关 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.toggle-slider {
    background-color: #2ecc71;
}

input:focus+.toggle-slider {
    box-shadow: 0 0 1px #2ecc71;
}

input:checked+.toggle-slider:before {
    transform: translateX(26px);
}

/* 警告提示 */
.alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.alert.warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
}

/* 冲突容器 */
#conflicts-container {
    max-height: 400px;
    overflow-y: auto;
}

.conflict-item {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 4px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.conflict-item .conflict-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.conflict-item .conflict-description {
    margin-bottom: 10px;
}

.conflict-item .conflict-suggestion {
    font-style: italic;
    color: #495057;
}

/* 大型模态框 */
.large-modal .modal-content {
    width: 85%;
    max-width: 1300px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 数据表格样式优化 */
.data-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.data-table th {
    background-color: #f8f9fa;
    padding: 15px;
    font-weight: 600;
    color: #2c3e50;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: 15px;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 按钮样式优化 */
.button {
    border-radius: 6px;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .main-content {
        padding: 20px;
    }

    .card {
        padding: 15px;
    }
}

@media (max-width: 992px) {
    .data-table {
        font-size: 0.9rem;
    }

    .data-table th,
    .data-table td {
        padding: 12px 10px;
    }
}

@media (max-width: 768px) {
    .model-info-panel {
        flex-direction: column;
        gap: 15px;
    }

    .config-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .rules-filter {
        flex-direction: column;
    }

    .large-modal .modal-content {
        width: 95%;
    }

    .button {
        width: 100%;
        margin-bottom: 5px;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .header .actions {
        margin-top: 15px;
        width: 100%;
        display: flex;
        justify-content: space-between;
    }
}