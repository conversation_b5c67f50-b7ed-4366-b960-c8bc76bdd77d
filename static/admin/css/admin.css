/* 管理控制台样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: white;
    padding: 20px 0;
}

.sidebar-header {
    padding: 0 20px 20px 20px;
    border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.sidebar-menu li {
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.sidebar-menu li:hover,
.sidebar-menu li.active {
    background-color: #34495e;
}

.sidebar-menu li a {
    color: white;
    text-decoration: none;
    display: block;
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.header h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #2c3e50;
}

.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: normal;
}

.stat-card .value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
}

.button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
}

.button.primary {
    background-color: #2ecc71;
    font-weight: bold;
    padding: 10px 20px;
}

.button:hover {
    background-color: #2980b9;
}

.button.secondary {
    background-color: #95a5a6;
}

.button.secondary:hover {
    background-color: #7f8c8d;
}

.button.danger {
    background-color: #e74c3c;
}

.button.danger:hover {
    background-color: #c0392b;
}

.button.success {
    background-color: #2ecc71;
}

.button.success:hover {
    background-color: #27ae60;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table th,
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

table tr:hover {
    background-color: #f5f5f5;
}

.badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.badge.success {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.badge.warning {
    background-color: #fff8e1;
    color: #f57f17;
}

.badge.danger {
    background-color: #ffebee;
    color: #c62828;
}

.badge.info {
    background-color: #e3f2fd;
    color: #1565c0;
}

.search-container {
    margin-bottom: 20px;
}

.search-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
}

.filter-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination button {
    padding: 5px 10px;
    margin: 0 5px;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    border-radius: 3px;
}

.pagination button.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.pagination button:hover:not(.active) {
    background-color: #f5f5f5;
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.alert.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert.danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-half {
    flex: 1;
    margin-bottom: 0;
}

.form-group-third {
    flex: 1;
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
}

.form-control:focus {
    border-color: #3498db;
    outline: none;
}

textarea.form-control {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.form-actions-bottom {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.form-tabs {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.tab-header {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.tab-item {
    padding: 10px 15px;
    cursor: pointer;
    font-weight: 600;
    color: #7f8c8d;
    transition: all 0.3s;
}

.tab-item.active {
    background-color: white;
    color: #3498db;
    border-bottom: 2px solid #3498db;
}

.tab-content {
    padding: 15px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 5px;
    width: 70%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.modal-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
}

.modal-close {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #ddd;
    padding-top: 15px;
}

/* 规则管理特定样式 */
.rule-priority {
    width: 60px;
    text-align: center;
}

.rule-enabled {
    width: 80px;
    text-align: center;
}

.rule-actions {
    width: 150px;
    text-align: center;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked+.toggle-slider {
    background-color: #2ecc71;
}

input:checked+.toggle-slider:before {
    transform: translateX(26px);
}

.pattern-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pattern-list li {
    padding: 5px 0;
    display: flex;
    align-items: center;
}

.pattern-list li button {
    margin-left: 10px;
}

.add-pattern-btn {
    margin-top: 10px;
}

.keyword-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.keyword-tag {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
}

.keyword-tag .remove-tag {
    margin-left: 5px;
    cursor: pointer;
    font-weight: bold;
}

.add-keyword-input {
    margin-top: 10px;
    display: flex;
}

.add-keyword-input input {
    flex: 1;
    margin-right: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 10px 0;
    }

    .stats-container {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 90%;
    }

    .filter-container {
        flex-direction: column;
    }

    .filter-select {
        width: 100%;
    }
}