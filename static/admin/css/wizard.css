/* 配置向导样式 */

/* 向导遮罩层 */
.wizard-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.wizard-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 向导模态框 */
.wizard-modal {
    background: var(--surface-color, #FFFFFF);
    border-radius: var(--radius-lg, 16px);
    width: 90%;
    max-width: 700px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(20px) scale(0.95);
    transition: transform 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.wizard-overlay.active .wizard-modal {
    transform: translateY(0) scale(1);
}

/* 向导头部 */
.wizard-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid var(--border-color, #E5E5EA);
    background: linear-gradient(135deg, #F8F9FA, #E9ECEF);
}

.wizard-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary, #000000);
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.wizard-title::before {
    content: '🎯';
    font-size: 28px;
}

.wizard-description {
    font-size: 16px;
    color: var(--text-secondary, #8E8E93);
    margin-bottom: 24px;
    line-height: 1.5;
}

/* 步骤指示器 */
.wizard-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    position: relative;
}

.wizard-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    z-index: 2;
}

.wizard-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 12px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: var(--border-color, #E5E5EA);
    z-index: 1;
    border-radius: 1px;
}

.wizard-step.active:not(:last-child)::after,
.wizard-step.completed:not(:last-child)::after {
    background: linear-gradient(90deg, var(--primary-color, #007AFF), var(--secondary-color, #5856D6));
}

.wizard-step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--border-color, #E5E5EA);
    color: var(--text-secondary, #8E8E93);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 3;
}

.wizard-step.active .wizard-step-number {
    background: linear-gradient(135deg, var(--primary-color, #007AFF), var(--secondary-color, #5856D6));
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.wizard-step.completed .wizard-step-number {
    background: linear-gradient(135deg, var(--success-color, #34C759), #30D158);
    color: white;
}

.wizard-step.completed .wizard-step-number::before {
    content: '✓';
    font-size: 16px;
    font-weight: bold;
}

.wizard-step-label {
    font-size: 12px;
    color: var(--text-secondary, #8E8E93);
    text-align: center;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.wizard-step.active .wizard-step-label {
    color: var(--text-primary, #000000);
    font-weight: 500;
}

/* 向导内容区 */
.wizard-body {
    padding: 24px;
    overflow-y: auto;
    max-height: 60vh;
}

.wizard-step-content {
    animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.wizard-step-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary, #000000);
    margin: 0 0 8px 0;
}

.wizard-step-description {
    font-size: 14px;
    color: var(--text-secondary, #8E8E93);
    margin-bottom: 24px;
    line-height: 1.5;
}

/* 选项容器 */
.wizard-options-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.wizard-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

/* 选项卡片 */
.wizard-option-card {
    padding: 20px;
    border: 2px solid var(--border-color, #E5E5EA);
    border-radius: var(--radius-md, 12px);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    background: var(--surface-color, #FFFFFF);
    position: relative;
    overflow: hidden;
}

.wizard-option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
    pointer-events: none;
}

.wizard-option-card:hover::before {
    left: 100%;
}

.wizard-option-card:hover {
    border-color: var(--primary-color, #007AFF);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 122, 255, 0.15);
}

.wizard-option-card.selected {
    border-color: var(--primary-color, #007AFF);
    background: rgba(0, 122, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 
        0 8px 24px rgba(0, 122, 255, 0.15),
        inset 0 0 0 1px rgba(0, 122, 255, 0.1);
}

.wizard-option-card.recommended {
    border-color: var(--success-color, #34C759);
    position: relative;
}

.wizard-option-card.recommended::before {
    background: linear-gradient(90deg, transparent, rgba(52, 199, 89, 0.2), transparent);
}

.recommended-badge {
    position: absolute;
    top: -8px;
    right: 12px;
    background: linear-gradient(135deg, var(--success-color, #34C759), #30D158);
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(52, 199, 89, 0.3);
}

/* 选项卡片内容 */
.wizard-option-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
}

.wizard-option-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm, 8px);
    background: linear-gradient(135deg, var(--primary-color, #007AFF), var(--secondary-color, #5856D6));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
}

.wizard-option-icon-large {
    font-size: 32px;
    margin-bottom: 12px;
    text-align: center;
}

.wizard-option-info {
    flex: 1;
}

.wizard-option-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #000000);
    margin: 0 0 4px 0;
}

.wizard-option-meta {
    font-size: 12px;
    color: var(--text-secondary, #8E8E93);
    display: flex;
    gap: 12px;
}

.wizard-option-description {
    font-size: 14px;
    color: var(--text-secondary, #8E8E93);
    line-height: 1.4;
    margin-bottom: 12px;
}

.wizard-option-features {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
}

.feature-tag {
    font-size: 11px;
    padding: 3px 8px;
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color, #007AFF);
    border-radius: 12px;
    border: 1px solid rgba(0, 122, 255, 0.2);
}

.wizard-option-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 11px;
    color: var(--text-secondary, #8E8E93);
    margin-bottom: 2px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color, #007AFF);
}

.wizard-option-use-cases {
    font-size: 12px;
    color: var(--text-secondary, #8E8E93);
}

.use-case-tag {
    display: inline-block;
    margin: 2px 4px 2px 0;
    padding: 2px 6px;
    background: rgba(142, 142, 147, 0.1);
    border-radius: 8px;
    font-size: 11px;
}

/* 高级选项 */
.wizard-advanced-options {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color, #E5E5EA);
}

.wizard-advanced-options details {
    cursor: pointer;
}

.wizard-advanced-options summary {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color, #007AFF);
    padding: 8px 0;
    list-style: none;
    outline: none;
}

.wizard-advanced-options summary::before {
    content: '▶';
    margin-right: 8px;
    transition: transform 0.2s ease;
}

.wizard-advanced-options details[open] summary::before {
    transform: rotate(90deg);
}

.advanced-options-content {
    padding: 16px 0;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 200px;
    }
}

/* 总结步骤 */
.summary-step {
    text-align: center;
}

.summary-icon {
    font-size: 64px;
    color: var(--success-color, #34C759);
    margin-bottom: 16px;
}

.configuration-summary {
    background: var(--surface-secondary, #F2F2F7);
    border-radius: var(--radius-md, 12px);
    padding: 20px;
    margin: 24px 0;
    text-align: left;
}

.summary-section {
    margin-bottom: 20px;
}

.summary-section:last-child {
    margin-bottom: 0;
}

.summary-section h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary, #000000);
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(142, 142, 147, 0.1);
}

.summary-item:last-child {
    border-bottom: none;
}

.item-label {
    font-size: 14px;
    color: var(--text-secondary, #8E8E93);
}

.item-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #000000);
}

.configuration-preview {
    background: rgba(52, 199, 89, 0.05);
    border: 1px solid rgba(52, 199, 89, 0.2);
    border-radius: var(--radius-md, 12px);
    padding: 20px;
    margin-top: 24px;
    text-align: left;
}

.configuration-preview h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary, #000000);
    margin: 0 0 12px 0;
}

.preview-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.preview-list li {
    display: flex;
    align-items: center;
    padding: 6px 0;
    font-size: 14px;
    color: var(--text-primary, #000000);
}

.preview-list li i {
    margin-right: 12px;
    width: 16px;
}

.text-success {
    color: var(--success-color, #34C759) !important;
}

/* 向导底部 */
.wizard-footer {
    padding: 20px 24px 24px 24px;
    border-top: 1px solid var(--border-color, #E5E5EA);
    background: var(--surface-secondary, #F2F2F7);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wizard-footer .btn {
    min-width: 100px;
}

.wizard-footer > div:last-child {
    display: flex;
    gap: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary, #000000);
    margin-bottom: 6px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color, #E5E5EA);
    border-radius: var(--radius-sm, 8px);
    font-size: 14px;
    background: var(--surface-color, #FFFFFF);
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color, #007AFF);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-help {
    font-size: 12px;
    color: var(--text-secondary, #8E8E93);
    margin-top: 4px;
    line-height: 1.4;
}

/* 按钮样式 */
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 空状态消息 */
.empty-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary, #8E8E93);
    font-style: italic;
    background: var(--surface-secondary, #F2F2F7);
    border-radius: var(--radius-md, 12px);
    border: 2px dashed var(--border-color, #E5E5EA);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .wizard-modal {
        width: 95%;
        max-height: 90vh;
    }

    .wizard-header {
        padding: 20px 20px 0 20px;
    }

    .wizard-body {
        padding: 20px;
        max-height: 65vh;
    }

    .wizard-footer {
        padding: 16px 20px 20px 20px;
        flex-direction: column;
        gap: 12px;
    }

    .wizard-footer > div:last-child {
        width: 100%;
        justify-content: center;
    }

    .wizard-options-grid {
        grid-template-columns: 1fr;
    }

    .wizard-steps {
        flex-wrap: wrap;
        gap: 8px;
    }

    .wizard-step {
        min-width: 60px;
    }

    .wizard-step:not(:last-child)::after {
        display: none;
    }

    .wizard-step-number {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .wizard-step-label {
        font-size: 11px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .wizard-overlay {
        background: rgba(0, 0, 0, 0.8);
    }
    
    .wizard-modal {
        background: #1C1C1E;
        color: #FFFFFF;
    }
    
    .wizard-header {
        background: linear-gradient(135deg, #2C2C2E, #3A3A3C);
        border-bottom-color: #38383A;
    }
    
    .wizard-footer {
        background: #2C2C2E;
        border-top-color: #38383A;
    }
    
    .configuration-summary {
        background: #2C2C2E;
    }
    
    .wizard-option-card {
        background: #2C2C2E;
        border-color: #38383A;
    }
    
    .wizard-option-card:hover {
        border-color: #0A84FF;
    }
    
    .wizard-option-card.selected {
        background: rgba(10, 132, 255, 0.1);
        border-color: #0A84FF;
    }
}