/* 队列状态表格样式 */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 15px;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    border-top: 1px solid #e9ecef;
    border-bottom: 2px solid #e9ecef;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .data-table {
        font-size: 14px;
    }
    
    .data-table th,
    .data-table td {
        padding: 10px 8px;
    }
}
