<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>安全事件中心 - 本地大模型防护系统</title>
    <link rel="stylesheet" href="/static/admin/css/apple-main.css">
    <script src="/static/admin/js/apple-ui.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 现代安全事件页面样式 */
        :root {
            --primary-color: #007AFF;
            --danger-color: #FF3B30;
            --warning-color: #FF9500;
            --success-color: #34C759;
            --info-color: #5AC8FA;
            --surface-color: #FFFFFF;
            --surface-secondary: #F2F2F7;
            --surface-tertiary: #E5E5EA;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --text-tertiary: #C7C7CC;
            --border-color: #C6C6C8;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
            --radius-xl: 20px;
        }

        /* 页面布局 */
        .events-container {
            padding: 32px;
            max-width: 1400px;
            margin: 0 auto;
            min-height: calc(100vh - 64px);
        }

        .page-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 32px;
            gap: 24px;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin: 0;
        }

        .page-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--accent-color);
        }

        .stat-card.critical::before { background: var(--danger-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.success::before { background: var(--success-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .stat-icon.critical { background: linear-gradient(135deg, var(--danger-color), #FF453A); }
        .stat-icon.warning { background: linear-gradient(135deg, var(--warning-color), #FFCC02); }
        .stat-icon.success { background: linear-gradient(135deg, var(--success-color), #30D158); }
        .stat-icon.info { background: linear-gradient(135deg, var(--info-color), #64D2FF); }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 20px;
        }

        .stat-trend.up {
            background: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .stat-trend.down {
            background: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .stat-value {
            font-size: 36px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 4px 0;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
            margin: 0;
        }

        .stat-detail {
            font-size: 12px;
            color: var(--text-tertiary);
            margin-top: 8px;
        }

        /* 高级过滤器 */
        .advanced-filters {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .filter-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .filter-input, .filter-select {
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 14px;
            background: var(--surface-color);
            transition: all 0.2s ease;
        }

        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .quick-filters {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .quick-filter {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid var(--border-color);
            background: var(--surface-color);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-filter:hover {
            background: var(--surface-secondary);
        }

        .quick-filter.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 事件列表 */
        .events-table-container {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
        }

        .table-header {
            padding: 20px 24px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .table-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .events-table {
            width: 100%;
            border-collapse: collapse;
        }

        .events-table th {
            background: var(--surface-secondary);
            padding: 16px 24px;
            text-align: left;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
        }

        .events-table td {
            padding: 20px 24px;
            border-bottom: 1px solid var(--surface-tertiary);
            vertical-align: middle;
        }

        .events-table tr:hover {
            background: var(--surface-secondary);
        }

        .event-severity {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .event-severity.critical {
            background: rgba(255, 59, 48, 0.1);
            color: var(--danger-color);
        }

        .event-severity.high {
            background: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .event-severity.medium {
            background: rgba(90, 200, 250, 0.1);
            color: var(--info-color);
        }

        .event-severity.low {
            background: rgba(52, 199, 89, 0.1);
            color: var(--success-color);
        }

        .event-type {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .event-type-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .event-time {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .event-rule {
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            background: var(--surface-secondary);
            padding: 4px 8px;
            border-radius: 6px;
            color: var(--text-primary);
        }

        .event-description {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .event-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 8px 12px;
            border: none;
            border-radius: var(--radius-sm);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .action-btn.primary {
            background: var(--primary-color);
            color: white;
        }

        .action-btn.secondary {
            background: var(--surface-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-top: 1px solid var(--border-color);
        }

        .pagination-info {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            background: var(--surface-color);
            color: var(--text-primary);
            border-radius: var(--radius-sm);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--surface-secondary);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .empty-state-description {
            font-size: 14px;
            margin-bottom: 24px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .events-container {
                padding: 16px;
            }

            .page-header {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .filter-controls {
                grid-template-columns: 1fr;
            }

            .events-table {
                font-size: 12px;
            }

            .events-table th,
            .events-table td {
                padding: 12px 16px;
            }

            .event-description {
                max-width: 200px;
            }
        }

        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 实时更新指示器 */
        .live-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: rgba(52, 199, 89, 0.1);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: var(--success-color);
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>

<body>
    <div class="container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNoaWVsZCI+PHBhdGggZD0iTTEyIDIycy04LTUtOC0xMlY1bDgtM2w4IDN2N2MwIDctOCAxMi04IDEyeiIvPjwvc3ZnPg=="
                        alt="Logo">
                    <h2>本地大模型防护系统</h2>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="monitor.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-desktop"></i></span>
                        <span class="sidebar-menu-text">监控中心</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-shield-alt"></i></span>
                        <span class="sidebar-menu-text">规则管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="model_rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-cogs"></i></span>
                        <span class="sidebar-menu-text">规则配置</span>
                    </a>
                </li>
                <li class="sidebar-menu-item active">
                    <a href="events.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-exclamation-triangle"></i></span>
                        <span class="sidebar-menu-text">安全事件</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="monitor.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-desktop"></i></span>
                        <span class="sidebar-menu-text">实时监控</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="models.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-brain"></i></span>
                        <span class="sidebar-menu-text">模型管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/index.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-comment-dots"></i></span>
                        <span class="sidebar-menu-text">模型测试</span>
                    </a>
                </li>
            </ul>
        </aside>

        <main class="main-content">
            <div class="events-container">
                <!-- 页面头部 -->
                <header class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">
                            <i class="fas fa-shield-alt"></i>
                            安全事件中心
                        </h1>
                        <p class="page-subtitle">实时监控和分析安全威胁事件，保护您的LLM应用安全</p>
                    </div>
                    <div class="page-actions">
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            实时监控
                        </div>
                        <button class="btn btn-secondary" onclick="exportEvents()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                        <button class="btn btn-primary" onclick="refreshEvents()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                    </div>
                </header>

                <!-- 统计概览 -->
                <section class="stats-grid">
                    <div class="stat-card critical">
                        <div class="stat-header">
                            <div class="stat-icon critical">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-arrow-up"></i>
                                +12%
                            </div>
                        </div>
                        <div class="stat-value" id="total-events">0</div>
                        <div class="stat-label">今日安全事件</div>
                        <div class="stat-detail">最近 24 小时内检测到的威胁</div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-header">
                            <div class="stat-icon warning">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="stat-trend down">
                                <i class="fas fa-arrow-down"></i>
                                -5%
                            </div>
                        </div>
                        <div class="stat-value" id="blocked-events">0</div>
                        <div class="stat-label">成功拦截</div>
                        <div class="stat-detail">有效阻止的威胁尝试</div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-header">
                            <div class="stat-icon info">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-arrow-up"></i>
                                +8%
                            </div>
                        </div>
                        <div class="stat-value" id="monitoring-rules">0</div>
                        <div class="stat-label">活跃规则</div>
                        <div class="stat-detail">当前启用的安全规则数量</div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-header">
                            <div class="stat-icon success">
                                <i class="fas fa-desktop"></i>
                            </div>
                            <div class="stat-trend up">
                                <i class="fas fa-arrow-up"></i>
                                +15%
                            </div>
                        </div>
                        <div class="stat-value" id="avg-response-time">0</div>
                        <div class="stat-label">平均响应时间</div>
                        <div class="stat-detail">毫秒 (ms) - 检测响应速度</div>
                    </div>
                </section>

                <!-- 高级过滤器 -->
                <section class="advanced-filters">
                    <div class="filter-header">
                        <h3 class="filter-title">事件过滤器</h3>
                        <button class="btn btn-sm btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            清除过滤
                        </button>
                    </div>

                    <div class="filter-controls">
                        <div class="filter-group">
                            <label class="filter-label">事件类型</label>
                            <select class="filter-select" id="event-type-filter">
                                <option value="">所有类型</option>
                                <option value="prompt_injection">提示注入</option>
                                <option value="harmful_content">有害内容</option>
                                <option value="sensitive_info">敏感信息</option>
                                <option value="jailbreak">越狱尝试</option>
                                <option value="adult_content">成人内容</option>
                                <option value="hate_speech">仇恨言论</option>
                                <option value="violence_content">暴力内容</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">威胁等级</label>
                            <select class="filter-select" id="severity-filter">
                                <option value="">所有等级</option>
                                <option value="critical">严重</option>
                                <option value="high">高危</option>
                                <option value="medium">中等</option>
                                <option value="low">低危</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">时间范围</label>
                            <select class="filter-select" id="time-range-filter">
                                <option value="1h">最近 1 小时</option>
                                <option value="6h">最近 6 小时</option>
                                <option value="24h" selected>最近 24 小时</option>
                                <option value="7d">最近 7 天</option>
                                <option value="30d">最近 30 天</option>
                                <option value="custom">自定义范围</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">搜索事件</label>
                            <input type="text" class="filter-input" id="search-input" placeholder="搜索规则ID、描述或关键词...">
                        </div>
                    </div>

                    <div class="quick-filters">
                        <div class="quick-filter active" onclick="setQuickFilter('all')">全部事件</div>
                        <div class="quick-filter" onclick="setQuickFilter('critical')">严重威胁</div>
                        <div class="quick-filter" onclick="setQuickFilter('recent')">最近 1 小时</div>
                        <div class="quick-filter" onclick="setQuickFilter('blocked')">已拦截</div>
                        <div class="quick-filter" onclick="setQuickFilter('unresolved')">待处理</div>
                    </div>
                </section>

                <!-- 事件列表 -->
                <section class="events-table-container">
                    <div class="table-header">
                        <h3 class="table-title">安全事件列表</h3>
                        <div class="table-actions">
                            <span class="text-secondary" id="events-count">加载中...</span>
                            <button class="btn btn-sm btn-secondary" onclick="toggleAutoRefresh()">
                                <i class="fas fa-pause" id="auto-refresh-icon"></i>
                                <span id="auto-refresh-text">自动刷新</span>
                            </button>
                        </div>
                    </div>

                    <div id="events-table-content">
                        <table class="events-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>威胁等级</th>
                                    <th>事件类型</th>
                                    <th>触发规则</th>
                                    <th>事件描述</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="events-table-body">
                                <!-- 事件数据将通过 JavaScript 动态加载 -->
                                <tr>
                                    <td colspan="7" style="text-align: center; padding: 40px;">
                                        <div class="loading-spinner"></div>
                                        <div style="margin-top: 16px; color: var(--text-secondary);">正在加载安全事件数据...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 <span id="page-start">0</span>-<span id="page-end">0</span> 条，共 <span id="total-count">0</span> 条事件
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" onclick="previousPage()">
                                <i class="fas fa-chevron-left"></i>
                                上一页
                            </button>
                            <div id="pagination-numbers"></div>
                            <button class="pagination-btn" onclick="nextPage()">
                                下一页
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- 事件详情模态框 -->
    <div id="event-details-modal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3>安全事件详情</h3>
                <button class="modal-close" onclick="closeEventDetails()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="event-details-content">
                <!-- 详情内容将动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentEvents = [];
        let filteredEvents = [];
        let currentPage = 1;
        let pageSize = 20;
        let autoRefreshEnabled = true;
        let autoRefreshInterval;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventsPage();
            setupEventListeners();
            startAutoRefresh();
        });

        // 初始化事件页面
        async function initializeEventsPage() {
            try {
                await loadEvents();
                await loadStats();
                updateEventsList();
            } catch (error) {
                console.error('初始化事件页面失败:', error);
                showError('加载数据失败: ' + error.message);
            }
        }

        // 加载事件数据
        async function loadEvents() {
            try {
                const response = await fetch('/api/v1/events?limit=1000');
                if (!response.ok) throw new Error('获取事件数据失败');
                
                const data = await response.json();
                const apiEvents = data.events || [];
                
                // 将API数据格式转换为前端期望的格式
                currentEvents = apiEvents.map(event => ({
                    id: event.event_id,
                    timestamp: new Date(event.timestamp * 1000).toISOString(), // 转换时间戳为ISO格式
                    type: event.detection_type,
                    typeName: getTypeDisplayName(event.detection_type),
                    typeIcon: getTypeIcon(event.detection_type),
                    severity: event.severity,
                    rule: event.details?.rule_id || event.rule_id || 'unknown',
                    description: event.reason || event.content || '未知威胁',
                    status: 'blocked', // API数据中的事件都是已拦截的
                    details: {
                        userInput: event.details?.matched_text || event.matched_text || '无详细信息',
                        modelResponse: '请求已被安全规则拦截',
                        confidence: 85, // 默认置信度
                        ruleId: event.details?.rule_id || event.rule_id,
                        ruleName: event.details?.rule_name || event.rule_name,
                        matchedPattern: event.details?.matched_pattern || event.matched_pattern
                    }
                }));
                
                filteredEvents = [...currentEvents];
                console.log('加载真实事件数据:', currentEvents.length, '条');
            } catch (error) {
                console.error('加载事件失败:', error);
                currentEvents = generateMockEvents(); // 生成模拟数据用于演示
                filteredEvents = [...currentEvents];
                console.log('使用模拟数据:', currentEvents.length, '条');
            }
        }

        // 加载统计数据
        async function loadStats() {
            try {
                // 使用events数据计算统计信息
                const stats = {
                    totalEvents: currentEvents.length,
                    blockedEvents: currentEvents.filter(e => e.status === 'blocked').length,
                    activeRules: [...new Set(currentEvents.map(e => e.rule))].length, // 去重统计活跃规则数
                    avgResponseTime: 45 // 默认响应时间
                };
                
                updateStats(stats);
            } catch (error) {
                console.error('加载统计数据失败:', error);
                updateStats(generateMockStats());
            }
        }

        // 更新统计显示
        function updateStats(stats) {
            document.getElementById('total-events').textContent = stats.totalEvents || 0;
            document.getElementById('blocked-events').textContent = stats.blockedEvents || 0;
            document.getElementById('monitoring-rules').textContent = stats.activeRules || 0;
            document.getElementById('avg-response-time').textContent = stats.avgResponseTime || 0;
        }

        // 生成模拟事件数据
        function generateMockEvents() {
            const eventTypes = [
                { type: 'harmful_content', name: '有害内容', icon: '⚠️' },
                { type: 'prompt_injection', name: '提示注入', icon: '🔓' },
                { type: 'sensitive_info', name: '敏感信息', icon: '🔐' },
                { type: 'jailbreak', name: '越狱尝试', icon: '🚨' },
                { type: 'adult_content', name: '成人内容', icon: '🔞' },
                { type: 'hate_speech', name: '仇恨言论', icon: '⚖️' },
                { type: 'violence_content', name: '暴力内容', icon: '⚔️' }
            ];

            const severities = ['critical', 'high', 'medium', 'low'];
            const rules = ['hc-001', 'pi-001', 'si-001', 'jb-001', 'ac-001', 'hs-001', 'vc-001'];
            const descriptions = [
                'Detected Violence Content: 制作炸弹',
                'Detected Prompt Injection: 忽略之前的指令',
                'Detected Sensitive Info: 信用卡号码泄露',
                'Detected Jailbreak Attempt: DAN模式激活',
                'Detected Adult Content: 不当性内容',
                'Detected Hate Speech: 种族歧视言论',
                'Detected Violence: 暴力威胁内容'
            ];

            const events = [];
            const now = new Date();

            for (let i = 0; i < 50; i++) {
                const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
                const severity = severities[Math.floor(Math.random() * severities.length)];
                const rule = rules[Math.floor(Math.random() * rules.length)];
                const description = descriptions[Math.floor(Math.random() * descriptions.length)];
                
                const eventTime = new Date(now - Math.random() * 24 * 60 * 60 * 1000);
                
                events.push({
                    id: `event-${i + 1}`,
                    timestamp: eventTime.toISOString(),
                    type: eventType.type,
                    typeName: eventType.name,
                    typeIcon: eventType.icon,
                    severity: severity,
                    rule: rule,
                    description: description,
                    status: Math.random() > 0.8 ? 'unresolved' : 'blocked',
                    details: {
                        userInput: `模拟用户输入内容 ${i + 1}`,
                        modelResponse: '请求已被安全规则拦截',
                        confidence: Math.floor(Math.random() * 40) + 60
                    }
                });
            }

            return events.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        }

        // 生成模拟统计数据
        function generateMockStats() {
            return {
                totalEvents: Math.floor(Math.random() * 200) + 100,
                blockedEvents: Math.floor(Math.random() * 150) + 80,
                activeRules: Math.floor(Math.random() * 20) + 15,
                avgResponseTime: Math.floor(Math.random() * 50) + 10
            };
        }

        // 更新事件列表显示
        function updateEventsList() {
            const tbody = document.getElementById('events-table-body');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageEvents = filteredEvents.slice(startIndex, endIndex);

            if (pageEvents.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7">
                            <div class="empty-state">
                                <div class="empty-state-icon">🔍</div>
                                <div class="empty-state-title">暂无安全事件</div>
                                <div class="empty-state-description">当前筛选条件下没有找到匹配的安全事件</div>
                            </div>
                        </td>
                    </tr>
                `;
            } else {
                tbody.innerHTML = pageEvents.map(event => `
                    <tr onclick="showEventDetails('${event.id}')">
                        <td>
                            <div class="event-time">
                                ${formatTime(event.timestamp)}
                            </div>
                        </td>
                        <td>
                            <div class="event-severity ${event.severity}">
                                ${getSeverityName(event.severity)}
                            </div>
                        </td>
                        <td>
                            <div class="event-type">
                                <div class="event-type-icon" style="background: ${getTypeColor(event.type)};">
                                    ${event.typeIcon}
                                </div>
                                ${event.typeName}
                            </div>
                        </td>
                        <td>
                            <div class="event-rule">${event.rule}</div>
                        </td>
                        <td>
                            <div class="event-description" title="${event.description}">
                                ${event.description}
                            </div>
                        </td>
                        <td>
                            <div class="event-severity ${event.status === 'blocked' ? 'success' : 'warning'}">
                                ${event.status === 'blocked' ? '已拦截' : '待处理'}
                            </div>
                        </td>
                        <td>
                            <div class="event-actions">
                                <button class="action-btn primary" onclick="event.stopPropagation(); showEventDetails('${event.id}')">
                                    <i class="fas fa-eye"></i>
                                    详情
                                </button>
                                <button class="action-btn secondary" onclick="event.stopPropagation(); resolveEvent('${event.id}')">
                                    <i class="fas fa-check"></i>
                                    处理
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }

            updatePagination();
            updateEventsCount();
        }

        // 更新分页信息
        function updatePagination() {
            const totalPages = Math.ceil(filteredEvents.length / pageSize);
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, filteredEvents.length);

            document.getElementById('page-start').textContent = startIndex;
            document.getElementById('page-end').textContent = endIndex;
            document.getElementById('total-count').textContent = filteredEvents.length;

            // 生成页码按钮
            const numbersContainer = document.getElementById('pagination-numbers');
            let numbersHTML = '';

            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                numbersHTML += `
                    <button class="pagination-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            numbersContainer.innerHTML = numbersHTML;

            // 更新上一页/下一页按钮状态
            const prevBtn = document.querySelector('.pagination-controls .pagination-btn:first-child');
            const nextBtn = document.querySelector('.pagination-controls .pagination-btn:last-child');
            
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }

        // 更新事件计数
        function updateEventsCount() {
            document.getElementById('events-count').textContent = `共 ${filteredEvents.length} 条事件`;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索输入
            document.getElementById('search-input').addEventListener('input', debounce(applyFilters, 300));
            
            // 过滤器选择
            document.getElementById('event-type-filter').addEventListener('change', applyFilters);
            document.getElementById('severity-filter').addEventListener('change', applyFilters);
            document.getElementById('time-range-filter').addEventListener('change', applyFilters);
        }

        // 应用过滤器
        function applyFilters() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const eventType = document.getElementById('event-type-filter').value;
            const severity = document.getElementById('severity-filter').value;
            const timeRange = document.getElementById('time-range-filter').value;

            filteredEvents = currentEvents.filter(event => {
                // 搜索过滤
                if (searchTerm && !event.description.toLowerCase().includes(searchTerm) && 
                    !event.rule.toLowerCase().includes(searchTerm)) {
                    return false;
                }

                // 类型过滤
                if (eventType && event.type !== eventType) {
                    return false;
                }

                // 严重程度过滤
                if (severity && event.severity !== severity) {
                    return false;
                }

                // 时间范围过滤
                if (timeRange !== '') {
                    const eventTime = new Date(event.timestamp);
                    const now = new Date();
                    const diffHours = (now - eventTime) / (1000 * 60 * 60);

                    switch (timeRange) {
                        case '1h':
                            if (diffHours > 1) return false;
                            break;
                        case '6h':
                            if (diffHours > 6) return false;
                            break;
                        case '24h':
                            if (diffHours > 24) return false;
                            break;
                        case '7d':
                            if (diffHours > 168) return false;
                            break;
                        case '30d':
                            if (diffHours > 720) return false;
                            break;
                    }
                }

                return true;
            });

            currentPage = 1;
            updateEventsList();
        }

        // 快速过滤器
        function setQuickFilter(filter) {
            // 更新快速过滤器按钮状态
            document.querySelectorAll('.quick-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 重置过滤器
            document.getElementById('search-input').value = '';
            document.getElementById('event-type-filter').value = '';
            document.getElementById('severity-filter').value = '';

            switch (filter) {
                case 'all':
                    document.getElementById('time-range-filter').value = '24h';
                    break;
                case 'critical':
                    document.getElementById('severity-filter').value = 'critical';
                    break;
                case 'recent':
                    document.getElementById('time-range-filter').value = '1h';
                    break;
                case 'blocked':
                    // 这里可以添加状态过滤逻辑
                    break;
                case 'unresolved':
                    // 这里可以添加未解决事件过滤逻辑
                    break;
            }

            applyFilters();
        }

        // 清除所有过滤器
        function clearFilters() {
            document.getElementById('search-input').value = '';
            document.getElementById('event-type-filter').value = '';
            document.getElementById('severity-filter').value = '';
            document.getElementById('time-range-filter').value = '24h';

            document.querySelectorAll('.quick-filter').forEach(btn => btn.classList.remove('active'));
            document.querySelector('.quick-filter').classList.add('active');

            applyFilters();
        }

        // 分页控制
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                updateEventsList();
            }
        }

        function nextPage() {
            const totalPages = Math.ceil(filteredEvents.length / pageSize);
            if (currentPage < totalPages) {
                currentPage++;
                updateEventsList();
            }
        }

        function goToPage(page) {
            currentPage = page;
            updateEventsList();
        }

        // 显示事件详情
        function showEventDetails(eventId) {
            const event = currentEvents.find(e => e.id === eventId);
            if (!event) return;

            const modal = document.getElementById('event-details-modal');
            const content = document.getElementById('event-details-content');

            content.innerHTML = `
                <div style="display: grid; gap: 24px;">
                    <div class="detail-section">
                        <h4>基本信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">事件ID</span>
                                <span class="detail-value">${event.id}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">发生时间</span>
                                <span class="detail-value">${formatTime(event.timestamp)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">威胁等级</span>
                                <span class="detail-value">
                                    <div class="event-severity ${event.severity}">
                                        ${getSeverityName(event.severity)}
                                    </div>
                                </span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">事件类型</span>
                                <span class="detail-value">${event.typeName}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">触发规则</span>
                                <span class="detail-value">
                                    <code class="event-rule">${event.rule}</code>
                                </span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">处理状态</span>
                                <span class="detail-value">
                                    <div class="event-severity ${event.status === 'blocked' ? 'success' : 'warning'}">
                                        ${event.status === 'blocked' ? '已拦截' : '待处理'}
                                    </div>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>事件描述</h4>
                        <div class="detail-description">
                            ${event.description}
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4>详细信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item full-width">
                                <span class="detail-label">用户输入</span>
                                <div class="detail-code">
                                    ${event.details.userInput}
                                </div>
                            </div>
                            <div class="detail-item full-width">
                                <span class="detail-label">系统响应</span>
                                <div class="detail-code">
                                    ${event.details.modelResponse}
                                </div>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">检测置信度</span>
                                <span class="detail-value">${event.details.confidence}%</span>
                            </div>
                        </div>
                    </div>

                    <div class="detail-actions">
                        <button class="btn btn-primary" onclick="resolveEvent('${event.id}'); closeEventDetails();">
                            <i class="fas fa-check"></i>
                            标记为已处理
                        </button>
                        <button class="btn btn-secondary" onclick="exportEventReport('${event.id}')">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>
            `;

            modal.style.display = 'flex';
        }

        // 关闭事件详情
        function closeEventDetails() {
            document.getElementById('event-details-modal').style.display = 'none';
        }

        // 处理事件
        function resolveEvent(eventId) {
            const event = currentEvents.find(e => e.id === eventId);
            if (event) {
                event.status = 'resolved';
                updateEventsList();
                showSuccess(`事件 ${eventId} 已标记为已处理`);
            }
        }

        // 刷新事件数据
        async function refreshEvents() {
            const refreshBtn = document.querySelector('.page-actions .btn-primary');
            const originalText = refreshBtn.innerHTML;
            
            refreshBtn.innerHTML = '<div class="loading-spinner"></div> 刷新中...';
            refreshBtn.disabled = true;

            try {
                await loadEvents();
                await loadStats();
                applyFilters();
                showSuccess('数据刷新成功');
            } catch (error) {
                showError('刷新失败: ' + error.message);
            } finally {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }
        }

        // 自动刷新控制
        function startAutoRefresh() {
            if (autoRefreshEnabled) {
                autoRefreshInterval = setInterval(async () => {
                    try {
                        await loadEvents();
                        await loadStats();
                        applyFilters();
                    } catch (error) {
                        console.error('自动刷新失败:', error);
                    }
                }, 30000); // 30秒刷新一次
            }
        }

        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const icon = document.getElementById('auto-refresh-icon');
            const text = document.getElementById('auto-refresh-text');

            if (autoRefreshEnabled) {
                icon.className = 'fas fa-pause';
                text.textContent = '自动刷新';
                startAutoRefresh();
            } else {
                icon.className = 'fas fa-play';
                text.textContent = '已暂停';
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            }
        }

        // 导出功能
        function exportEvents() {
            const data = filteredEvents.map(event => ({
                时间: formatTime(event.timestamp),
                威胁等级: getSeverityName(event.severity),
                事件类型: event.typeName,
                触发规则: event.rule,
                事件描述: event.description,
                状态: event.status === 'blocked' ? '已拦截' : '待处理'
            }));

            const csvContent = convertToCSV(data);
            downloadCSV(csvContent, `security-events-${new Date().toISOString().split('T')[0]}.csv`);
        }

        function exportEventReport(eventId) {
            const event = currentEvents.find(e => e.id === eventId);
            if (event) {
                const report = generateEventReport(event);
                downloadJSON(report, `event-report-${eventId}.json`);
            }
        }

        // 工具函数
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function getSeverityName(severity) {
            const names = {
                critical: '严重',
                high: '高危',
                medium: '中等',
                low: '低危'
            };
            return names[severity] || severity;
        }

        function getTypeColor(type) {
            const colors = {
                harmful_content: '#FF3B30',
                prompt_injection: '#FF9500',
                sensitive_info: '#007AFF',
                jailbreak: '#5856D6',
                adult_content: '#FF2D92',
                hate_speech: '#8E8E93',
                violence_content: '#FF6B35'
            };
            return colors[type] || '#8E8E93';
        }

        function getTypeDisplayName(type) {
            const names = {
                harmful_content: '有害内容',
                prompt_injection: '提示注入',
                sensitive_info: '敏感信息',
                jailbreak: '越狱尝试',
                adult_content: '成人内容',
                hate_speech: '仇恨言论',
                violence_content: '暴力内容'
            };
            return names[type] || type;
        }

        function getTypeIcon(type) {
            const icons = {
                harmful_content: '⚠️',
                prompt_injection: '🔓',
                sensitive_info: '🔐',
                jailbreak: '🚨',
                adult_content: '🔞',
                hate_speech: '⚖️',
                violence_content: '⚔️'
            };
            return icons[type] || '⚠️';
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function convertToCSV(data) {
            if (!data.length) return '';
            
            const headers = Object.keys(data[0]);
            const csvRows = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
            ];
            
            return csvRows.join('\n');
        }

        function downloadCSV(csvContent, filename) {
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function downloadJSON(data, filename) {
            const jsonContent = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function generateEventReport(event) {
            return {
                eventId: event.id,
                timestamp: event.timestamp,
                type: event.type,
                severity: event.severity,
                rule: event.rule,
                description: event.description,
                status: event.status,
                details: event.details,
                generatedAt: new Date().toISOString(),
                reportVersion: '1.0'
            };
        }

        // 消息提示函数
        function showSuccess(message) {
            // 这里可以实现成功消息提示
            console.log('Success:', message);
        }

        function showError(message) {
            // 这里可以实现错误消息提示
            console.error('Error:', message);
        }

        // 模态框样式
        const modalStyles = `
            <style>
                .modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                }

                .modal-content {
                    background: var(--surface-color);
                    border-radius: var(--radius-lg);
                    box-shadow: var(--shadow-lg);
                    width: 90%;
                    max-height: 90vh;
                    overflow-y: auto;
                }

                .modal-header {
                    padding: 24px 24px 0 24px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid var(--border-color);
                    margin-bottom: 24px;
                }

                .modal-header h3 {
                    margin: 0;
                    font-size: 20px;
                    font-weight: 600;
                    color: var(--text-primary);
                }

                .modal-close {
                    padding: 8px;
                    border: none;
                    background: none;
                    font-size: 18px;
                    color: var(--text-secondary);
                    cursor: pointer;
                    border-radius: var(--radius-sm);
                    transition: all 0.2s ease;
                }

                .modal-close:hover {
                    background: var(--surface-secondary);
                    color: var(--text-primary);
                }

                .modal-body {
                    padding: 0 24px 24px 24px;
                }

                .detail-section {
                    margin-bottom: 24px;
                }

                .detail-section h4 {
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--text-primary);
                    margin: 0 0 16px 0;
                    padding-bottom: 8px;
                    border-bottom: 1px solid var(--border-color);
                }

                .detail-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 16px;
                }

                .detail-item {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                .detail-item.full-width {
                    grid-column: 1 / -1;
                }

                .detail-label {
                    font-size: 12px;
                    font-weight: 600;
                    color: var(--text-secondary);
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .detail-value {
                    font-size: 14px;
                    color: var(--text-primary);
                    font-weight: 500;
                }

                .detail-description {
                    font-size: 14px;
                    color: var(--text-primary);
                    line-height: 1.6;
                    padding: 16px;
                    background: var(--surface-secondary);
                    border-radius: var(--radius-sm);
                }

                .detail-code {
                    font-family: 'SF Mono', Monaco, monospace;
                    font-size: 12px;
                    color: var(--text-primary);
                    background: var(--surface-secondary);
                    padding: 12px;
                    border-radius: var(--radius-sm);
                    border: 1px solid var(--border-color);
                    white-space: pre-wrap;
                    word-break: break-word;
                }

                .detail-actions {
                    display: flex;
                    gap: 12px;
                    padding-top: 16px;
                    border-top: 1px solid var(--border-color);
                }

                @media (max-width: 768px) {
                    .modal-content {
                        width: 95%;
                        margin: 20px 0;
                    }

                    .detail-grid {
                        grid-template-columns: 1fr;
                    }

                    .detail-actions {
                        flex-direction: column;
                    }
                }
            </style>
        `;

        // 添加模态框样式
        document.head.insertAdjacentHTML('beforeend', modalStyles);
    </script>
</body>

</html>