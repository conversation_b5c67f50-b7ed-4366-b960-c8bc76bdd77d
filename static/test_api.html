<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
        }
        .success { background: #e8f5e8; border: 1px solid #90ee90; }
        .error { background: #fee; border: 1px solid #faa; }
        button {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 8px;
            background: #007AFF;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056CC;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API连接测试工具</h1>
        
        <button onclick="testHealth()">测试健康检查</button>
        <button onclick="testModels()">测试模型列表</button>
        <button onclick="testChat()">测试聊天API</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        function addResult(title, content, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `
                <strong>${title}</strong><br>
                <pre>${JSON.stringify(content, null, 2)}</pre>
            `;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testHealth() {
            try {
                const response = await fetch('/api/v1/health');
                const data = await response.json();
                addResult('✅ 健康检查', data);
            } catch (error) {
                addResult('❌ 健康检查失败', error.message, true);
            }
        }

        async function testModels() {
            try {
                const response = await fetch('/api/v1/ollama/models', {
                    headers: {
                        'Authorization': 'Bearer cherry-studio-key'
                    }
                });
                const data = await response.json();
                addResult('✅ 模型列表', data);
            } catch (error) {
                addResult('❌ 模型列表获取失败', error.message, true);
            }
        }

        async function testChat() {
            try {
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer cherry-studio-key'
                    },
                    body: JSON.stringify({
                        model: 'qwen3:latest',
                        messages: [{
                            role: 'user',
                            content: '你好'
                        }],
                        stream: false
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addResult('✅ 聊天API测试', data);
            } catch (error) {
                addResult('❌ 聊天API测试失败', {
                    message: error.message,
                    stack: error.stack
                }, true);
            }
        }
    </script>
</body>
</html>