<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>模型测试中心 - 本地大模型防护系统</title>
    <link rel="stylesheet" href="/static/admin/css/apple-main.css">
    <script src="/static/admin/js/apple-ui.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css">
    <style>
        /* 专业模型测试界面样式 */
        :root {
            --primary-color: #007AFF;
            --secondary-color: #5856D6;
            --success-color: #34C759;
            --warning-color: #FF9500;
            --danger-color: #FF3B30;
            --info-color: #5AC8FA;
            --surface-color: #FFFFFF;
            --surface-secondary: #F2F2F7;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --border-color: #C6C6C8;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
            --radius-sm: 8px;
            --radius-md: 12px;
            --radius-lg: 16px;
        }

        /* 测试中心主布局 */
        .testing-center {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
            height: calc(100vh - 120px);
            overflow: hidden;
        }

        /* 控制面板 */
        .control-panel {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: linear-gradient(135deg, var(--surface-secondary), #E5E5EA);
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .panel-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        /* 模型选择器 */
        .model-selector {
            margin-bottom: 24px;
        }

        .model-selector label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .model-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--surface-color);
            font-size: 14px;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .model-info {
            margin-top: 8px;
            padding: 12px;
            background: var(--surface-secondary);
            border-radius: var(--radius-sm);
            font-size: 13px;
            color: var(--text-secondary);
        }

        /* 参数配置 */
        .parameter-group {
            margin-bottom: 20px;
        }

        .parameter-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .parameter-value {
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            color: var(--primary-color);
            font-weight: 600;
        }

        .parameter-slider {
            width: 100%;
            -webkit-appearance: none;
            height: 4px;
            border-radius: 2px;
            background: var(--surface-secondary);
            outline: none;
            margin-bottom: 8px;
        }

        .parameter-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .parameter-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary-color);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* 系统状态 */
        .system-status {
            margin-bottom: 20px;
            padding: 16px;
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        .status-label {
            color: var(--text-secondary);
        }

        .status-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-left: 8px;
        }

        .status-indicator.online { background: var(--success-color); }
        .status-indicator.warning { background: var(--warning-color); }
        .status-indicator.offline { background: var(--danger-color); }

        /* 对话区域 */
        .chat-area {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, var(--surface-secondary), #E5E5EA);
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0;
        }

        .chat-actions {
            display: flex;
            gap: 8px;
        }

        .chat-action-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            background: var(--surface-color);
            color: var(--text-primary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chat-action-btn:hover {
            background: var(--surface-secondary);
            transform: translateY(-1px);
        }

        /* 消息容器 */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            min-height: 0;
        }

        .message {
            display: flex;
            gap: 12px;
            max-width: 85%;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message.assistant {
            align-self: flex-start;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }

        .message-avatar.security {
            background: var(--danger-color);
        }

        .message-content.security {
            background: linear-gradient(135deg, rgba(255, 59, 48, 0.1), rgba(255, 149, 0, 0.1));
            border-left: 4px solid var(--danger-color);
            color: var(--text-primary);
            border-bottom-left-radius: 4px;
        }

        .message.security-message {
            align-self: flex-start;
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .message.security-message .message-content strong {
            color: var(--danger-color);
            font-weight: 700;
        }

        .message-avatar.user {
            background: #1D4ED8;
        }

        .message-avatar.assistant {
            background: #64748B;
        }

        .message-content {
            flex: 1;
            padding: 16px 20px;
            border-radius: var(--radius-lg);
            position: relative;
            line-height: 1.6;
        }

        .message-content.user {
            background: #E8F4FD;
            color: #1D4ED8;
            border: 1px solid #BFDBFE;
            border-bottom-right-radius: 4px;
        }

        .message-content.assistant {
            background: #F8FAFC;
            color: #334155;
            border: 1px solid #E2E8F0;
            border-bottom-left-radius: 4px;
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .message-time {
            opacity: 0.7;
        }

        .message-actions {
            display: flex;
            gap: 8px;
        }

        .message-action {
            background: none;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 11px;
            transition: all 0.2s ease;
        }

        .message-action:hover {
            background: rgba(0, 0, 0, 0.05);
            color: var(--text-primary);
        }

        /* 代码高亮 */
        .message-content pre {
            background: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.45;
        }

        .message-content code {
            background: rgba(175, 184, 193, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
            font-size: 13px;
        }

        /* 输入区域 */
        .input-area {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            background: var(--surface-color);
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 48px;
            max-height: 120px;
            padding: 14px 50px 14px 16px;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--surface-color);
            color: var(--text-primary);
            font-size: 16px;
            line-height: 1.4;
            resize: none;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .input-actions {
            position: absolute;
            right: 8px;
            bottom: 8px;
            display: flex;
            gap: 4px;
        }

        .input-action {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 6px;
            background: var(--surface-secondary);
            color: var(--text-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .input-action:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .send-button {
            padding: 14px 24px;
            border: none;
            border-radius: var(--radius-md);
            background: var(--primary-color);
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 100px;
            justify-content: center;
        }

        .send-button:hover:not(:disabled) {
            background: #0056CC;
            transform: translateY(-1px);
        }

        .send-button:disabled {
            background: var(--border-color);
            color: var(--text-secondary);
            cursor: not-allowed;
            transform: none;
        }

        /* 加载状态 */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 16px 20px;
            background: var(--surface-secondary);
            border-radius: var(--radius-lg);
            margin: 16px 0;
            border-bottom-left-radius: 4px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--text-secondary);
            animation: bounce 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes bounce {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        /* 安全警告 */
        .security-warning {
            background: rgba(255, 59, 48, 0.1);
            border: 1px solid var(--danger-color);
            border-radius: var(--radius-sm);
            padding: 12px;
            margin: 16px 0;
            font-size: 14px;
            color: var(--danger-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .testing-center {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .control-panel {
                order: 2;
            }
            
            .chat-area {
                order: 1;
                height: 60vh;
            }
        }

        /* 暗色模式 */
        .dark-mode {
            --surface-color: #1C1C1E;
            --surface-secondary: #2C2C2E;
            --text-primary: #FFFFFF;
            --text-secondary: #8E8E93;
            --border-color: #3A3A3C;
        }

        /* 滚动条样式 */
        .messages-container::-webkit-scrollbar,
        .panel-body::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track,
        .panel-body::-webkit-scrollbar-track {
            background: var(--surface-secondary);
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb,
        .panel-body::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover,
        .panel-body::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNoaWVsZCI+PHBhdGggZD0iTTEyIDIycy04LTUtOC0xMlY1bDgtM2w4IDN2N2MwIDctOCAxMi04IDEyeiIvPjwvc3ZnPg=="
                        alt="Logo">
                    <h2>本地大模型防护系统</h2>
                </div>
                <button class="sidebar-toggle">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="/static/admin/monitor.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-desktop"></i></span>
                        <span class="sidebar-menu-text">监控中心</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/admin/rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-shield-alt"></i></span>
                        <span class="sidebar-menu-text">规则管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/admin/model_rules.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-cogs"></i></span>
                        <span class="sidebar-menu-text">规则配置</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/admin/events.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-exclamation-triangle"></i></span>
                        <span class="sidebar-menu-text">安全事件</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/admin/models.html" class="sidebar-menu-link">
                        <span class="sidebar-menu-icon"><i class="fas fa-brain"></i></span>
                        <span class="sidebar-menu-text">模型管理</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/static/index.html" class="sidebar-menu-link active">
                        <span class="sidebar-menu-icon"><i class="fas fa-comment-dots"></i></span>
                        <span class="sidebar-menu-text">模型测试</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="d-flex align-items-center justify-content-center gap-sm">
                    <span>暗色模式</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="dark-mode-toggle">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
                <div class="mt-sm">版本 1.0.3</div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <nav class="top-navbar">
                <div class="navbar-left">
                    <button class="navbar-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="navbar-title">本地大模型防护系统</h1>
                </div>
                <div class="navbar-right">
                    <span id="current-time"></span>
                </div>
            </nav>

            <!-- 页面标题 -->
            <header class="page-header">
                <h1 class="page-title">模型测试中心</h1>
                <div class="page-actions">
                    <button class="button button-secondary" id="export-chat">
                        <i class="fas fa-download"></i> 导出对话
                    </button>
                    <button class="button" id="clear-chat">
                        <i class="fas fa-trash"></i> 清空对话
                    </button>
                </div>
            </header>

            <!-- 测试中心主体 -->
            <div class="testing-center">
                <!-- 控制面板 -->
                <div class="control-panel">
                    <div class="panel-header">
                        <h2 class="panel-title">
                            <i class="fas fa-sliders-h"></i>
                            测试配置
                        </h2>
                    </div>
                    <div class="panel-body">
                        <!-- 模型选择 -->
                        <div class="model-selector">
                            <label for="model-select">选择模型</label>
                            <select id="model-select" class="model-select">
                                <option value="">正在加载模型...</option>
                            </select>
                            <div class="model-info" id="model-info">
                                <div><strong>状态:</strong> <span id="model-status">未选择</span></div>
                                <div><strong>大小:</strong> <span id="model-size">-</span></div>
                                <div><strong>更新时间:</strong> <span id="model-modified">-</span></div>
                            </div>
                        </div>

                        <!-- 生成参数 -->
                        <div class="parameter-group">
                            <div class="parameter-label">
                                <span>温度 (Temperature)</span>
                                <span class="parameter-value" id="temperature-value">0.7</span>
                            </div>
                            <input type="range" id="temperature-slider" class="parameter-slider" 
                                   min="0" max="2" step="0.1" value="0.7">
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                控制生成文本的随机性和创造性
                            </div>
                        </div>

                        <div class="parameter-group">
                            <div class="parameter-label">
                                <span>最大令牌数 (Max Tokens)</span>
                                <span class="parameter-value" id="max-tokens-value">2048</span>
                            </div>
                            <input type="range" id="max-tokens-slider" class="parameter-slider" 
                                   min="1" max="4096" step="1" value="2048">
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                限制生成响应的最大长度
                            </div>
                        </div>

                        <div class="parameter-group">
                            <div class="parameter-label">
                                <span>Top-p (Nucleus Sampling)</span>
                                <span class="parameter-value" id="top-p-value">0.9</span>
                            </div>
                            <input type="range" id="top-p-slider" class="parameter-slider" 
                                   min="0" max="1" step="0.01" value="0.9">
                            <div style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">
                                控制生成词汇的多样性
                            </div>
                        </div>

                        <!-- 系统状态 -->
                        <div class="system-status">
                            <h3 style="margin: 0 0 12px 0; font-size: 14px; font-weight: 600;">系统状态</h3>
                            <div class="status-item">
                                <span class="status-label">API 连接</span>
                                <div style="display: flex; align-items: center;">
                                    <span class="status-value" id="api-status">检查中</span>
                                    <div class="status-indicator" id="api-indicator"></div>
                                </div>
                            </div>
                            <div class="status-item">
                                <span class="status-label">安全检测</span>
                                <div style="display: flex; align-items: center;">
                                    <span class="status-value" id="security-status">启用</span>
                                    <div class="status-indicator online"></div>
                                </div>
                            </div>
                            <div class="status-item">
                                <span class="status-label">响应时间</span>
                                <span class="status-value" id="response-time">- ms</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">已发送消息</span>
                                <span class="status-value" id="message-count">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对话区域 -->
                <div class="chat-area">
                    <div class="chat-header">
                        <h2 class="chat-title">对话测试</h2>
                        <div class="chat-actions">
                            <button class="chat-action-btn" id="save-session">
                                <i class="fas fa-save"></i> 保存会话
                            </button>
                            <button class="chat-action-btn" id="load-session">
                                <i class="fas fa-folder-open"></i> 加载会话
                            </button>
                        </div>
                    </div>

                    <div class="messages-container" id="messages-container">
                        <!-- 欢迎消息 -->
                        <div class="message assistant">
                            <div class="message-avatar assistant">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content assistant">
                                <div>
                                    <strong>欢迎使用模型测试中心！</strong><br><br>
                                    
                                    这里您可以：<br>
                                    • 与不同的大语言模型进行对话测试<br>
                                    • 调整模型参数以获得最佳效果<br>
                                    • 实时查看安全检测状态<br>
                                    • 导出和保存对话记录<br><br>
                                    
                                    请先在左侧选择一个模型，然后开始您的测试之旅！
                                </div>
                                <div class="message-meta">
                                    <span class="message-time" id="welcome-time"></span>
                                    <div class="message-actions">
                                        <button class="message-action" onclick="copyMessage(this)">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="input-area">
                        <div class="input-container">
                            <div class="input-wrapper">
                                <textarea id="message-input" class="message-input" 
                                          placeholder="输入您的消息... (Shift+Enter 换行，Enter 发送)"
                                          rows="1"></textarea>
                                <div class="input-actions">
                                    <button class="input-action" id="clear-input" title="清空输入">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button class="input-action" id="paste-input" title="粘贴">
                                        <i class="fas fa-paste"></i>
                                    </button>
                                </div>
                            </div>
                            <button id="send-button" class="send-button" disabled>
                                <i class="fas fa-paper-plane"></i>
                                发送
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 全局变量
        let currentModel = null;
        let messageCount = 0;
        let conversationHistory = [];
        let isGenerating = false;

        // 参数配置
        let modelParams = {
            temperature: 0.7,
            max_tokens: 2048,
            top_p: 0.9
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTestingCenter();
            setupEventListeners();
            loadAvailableModels();
            updateWelcomeTime();
            checkSystemStatus();
        });

        function initializeTestingCenter() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            // 初始化Marked.js
            if (typeof marked !== 'undefined') {
                marked.setOptions({
                    highlight: function(code, language) {
                        if (typeof hljs !== 'undefined' && language && hljs.getLanguage(language)) {
                            try {
                                return hljs.highlight(code, { language: language }).value;
                            } catch (err) {}
                        }
                        return code;
                    },
                    breaks: true,
                    gfm: true
                });
            }
        }

        function setupEventListeners() {
            // 参数滑块
            document.getElementById('temperature-slider').addEventListener('input', function() {
                const value = parseFloat(this.value);
                modelParams.temperature = value;
                document.getElementById('temperature-value').textContent = value.toFixed(1);
            });

            document.getElementById('max-tokens-slider').addEventListener('input', function() {
                const value = parseInt(this.value);
                modelParams.max_tokens = value;
                document.getElementById('max-tokens-value').textContent = value;
            });

            document.getElementById('top-p-slider').addEventListener('input', function() {
                const value = parseFloat(this.value);
                modelParams.top_p = value;
                document.getElementById('top-p-value').textContent = value.toFixed(2);
            });

            // 模型选择
            document.getElementById('model-select').addEventListener('change', function() {
                selectModel(this.value);
            });

            // 输入框
            const messageInput = document.getElementById('message-input');
            messageInput.addEventListener('input', autoResizeTextarea);
            messageInput.addEventListener('keydown', handleInputKeydown);

            // 按钮事件
            document.getElementById('send-button').addEventListener('click', sendMessage);
            document.getElementById('clear-chat').addEventListener('click', clearChat);
            document.getElementById('export-chat').addEventListener('click', exportChat);
            document.getElementById('clear-input').addEventListener('click', () => {
                messageInput.value = '';
                updateSendButton();
                autoResizeTextarea.call(messageInput);
            });

            // 检查输入状态
            messageInput.addEventListener('input', updateSendButton);
            updateSendButton();
        }

        function updateCurrentTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };
            document.getElementById('current-time').textContent = now.toLocaleDateString('zh-CN', options);
        }

        function updateWelcomeTime() {
            const now = new Date();
            document.getElementById('welcome-time').textContent = now.toLocaleTimeString('zh-CN');
        }

        function autoResizeTextarea() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        }

        function handleInputKeydown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                if (!isGenerating && this.value.trim()) {
                    sendMessage();
                }
            }
        }

        function updateSendButton() {
            const input = document.getElementById('message-input');
            const button = document.getElementById('send-button');
            const hasText = input.value.trim().length > 0;
            
            button.disabled = !hasText || isGenerating || !currentModel;
            
            if (isGenerating) {
                button.innerHTML = '<i class="fas fa-stop"></i> 停止';
            } else {
                button.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
            }
        }

        async function loadAvailableModels() {
            try {
                const response = await fetch('/api/v1/ollama/models', {
                    headers: {
                        'Authorization': 'Bearer cherry-studio-key'
                    }
                });

                if (!response.ok) {
                    throw new Error('获取模型列表失败');
                }

                const data = await response.json();
                const models = data.data || [];
                
                const select = document.getElementById('model-select');
                select.innerHTML = '<option value="">请选择模型</option>';
                
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.id} (${model.owned_by})`;
                    select.appendChild(option);
                });

                if (models.length > 0) {
                    updateApiStatus('online', '正常');
                } else {
                    updateApiStatus('warning', '无可用模型');
                }
            } catch (error) {
                console.error('加载模型失败:', error);
                updateApiStatus('offline', '连接失败');
                
                const select = document.getElementById('model-select');
                select.innerHTML = '<option value="">加载失败，请刷新重试</option>';
            }
        }

        function selectModel(modelId) {
            if (!modelId) {
                currentModel = null;
                updateModelInfo();
                updateSendButton();
                return;
            }

            currentModel = modelId;
            updateModelInfo({
                id: modelId,
                status: '就绪',
                size: '未知',
                modified: '未知'
            });
            updateSendButton();
        }

        function updateModelInfo(info = null) {
            if (!info) {
                document.getElementById('model-status').textContent = '未选择';
                document.getElementById('model-size').textContent = '-';
                document.getElementById('model-modified').textContent = '-';
                return;
            }

            document.getElementById('model-status').textContent = info.status;
            document.getElementById('model-size').textContent = info.size;
            document.getElementById('model-modified').textContent = info.modified;
        }

        function updateApiStatus(status, text) {
            const indicator = document.getElementById('api-indicator');
            const statusText = document.getElementById('api-status');
            
            indicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }

        async function checkSystemStatus() {
            try {
                const start = Date.now();
                const response = await fetch('/api/v1/health/status');
                const responseTime = Date.now() - start;
                
                document.getElementById('response-time').textContent = responseTime + ' ms';
                
                if (response.ok) {
                    updateApiStatus('online', '正常');
                } else {
                    updateApiStatus('warning', '部分异常');
                }
            } catch (error) {
                updateApiStatus('offline', '连接失败');
                document.getElementById('response-time').textContent = '超时';
            }
        }

        // 自定义错误类用于安全违规
        class SecurityViolationError extends Error {
            constructor(errorData) {
                super(errorData.error || '安全检测拦截');
                this.name = 'SecurityViolationError';
                this.errorData = errorData;
                this.type = errorData.type || 'security_violation';
                this.details = errorData.details || {};
            }
        }

        // 处理安全违规错误
        function handleSecurityViolation(error) {
            const errorData = error.errorData;
            let contextualMessage = '';
            let securityTips = [];

            // 根据检测到的规则类型生成上下文化的错误消息
            if (errorData.details && errorData.details.detection_type) {
                const detectionType = errorData.details.detection_type;
                
                switch (detectionType) {
                    case 'harmful_content':
                        contextualMessage = '🚫 **检测到有害内容**\n\n您的消息包含了可能有害的内容，包括暴力、危险行为或不当请求。';
                        securityTips = [
                            '请避免询问制作危险物品的方法',
                            '不要请求产生暴力或伤害性内容',
                            '尝试以教育或学术的角度重新表述您的问题'
                        ];
                        break;
                        
                    case 'prompt_injection':
                        contextualMessage = '⚠️ **检测到提示注入尝试**\n\n您的消息似乎试图绕过或操纵系统指令。';
                        securityTips = [
                            '请直接提出您的问题，不要尝试修改系统行为',
                            '避免要求忽略之前的指令',
                            '以自然的方式描述您需要的帮助'
                        ];
                        break;
                        
                    case 'jailbreak':
                        contextualMessage = '🔒 **检测到越狱尝试**\n\n您的消息包含试图绕过安全限制的内容。';
                        securityTips = [
                            '请不要尝试激活"无限制模式"',
                            '避免要求扮演没有限制的AI',
                            '以正当的方式提出您的问题'
                        ];
                        break;
                        
                    case 'sensitive_info':
                        contextualMessage = '🔐 **检测到敏感信息**\n\n您的消息可能包含敏感的个人或系统信息。';
                        securityTips = [
                            '请不要分享个人身份信息',
                            '避免包含密码、密钥或其他认证信息',
                            '使用示例数据替代真实的敏感信息'
                        ];
                        break;
                        
                    case 'role_play':
                        contextualMessage = '🎭 **检测到不当角色扮演**\n\n您的消息包含不适当的角色扮演请求。';
                        securityTips = [
                            '请避免要求扮演有害或危险的角色',
                            '不要请求模拟犯罪分子或恶意行为者',
                            '以建设性的方式进行对话'
                        ];
                        break;
                        
                    default:
                        contextualMessage = '🛡️ **触发安全检测**\n\n您的消息触发了安全防护机制。';
                        securityTips = [
                            '请重新审视您的问题内容',
                            '尝试以更清晰、更具体的方式表达',
                            '确保您的请求符合使用政策'
                        ];
                }
            } else {
                // 如果没有详细的检测类型信息，使用通用消息
                contextualMessage = '🛡️ **安全检测拦截**\n\n' + (errorData.error || '您的消息触发了安全防护机制。');
                securityTips = [
                    '请检查消息内容是否包含敏感词汇',
                    '尝试以不同的方式表达您的问题',
                    '确保请求内容符合使用规范'
                ];
            }

            // 添加检测到的具体原因
            if (errorData.details && errorData.details.reason) {
                contextualMessage += `\n\n**检测原因**: ${errorData.details.reason}`;
            }

            // 添加改进建议
            if (securityTips.length > 0) {
                contextualMessage += '\n\n**改进建议**:\n';
                securityTips.forEach((tip, index) => {
                    contextualMessage += `${index + 1}. ${tip}\n`;
                });
            }

            // 显示上下文化的安全消息
            addSecurityMessage(contextualMessage);
            
            // 显示输入建议
            if (errorData.details && errorData.details.current_input) {
                showInputAnalysis(errorData.details.current_input);
            }
        }

        // 添加安全相关的消息（与普通消息样式不同）
        function addSecurityMessage(content) {
            const container = document.getElementById('messages-container');
            const message = document.createElement('div');
            message.className = 'message security-message';
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            
            // 处理Markdown内容
            let processedContent = content;
            if (typeof marked !== 'undefined') {
                processedContent = marked.parse(content);
            } else {
                // 简单的Markdown处理
                processedContent = content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/^\d+\.\s/gm, '<li>')
                    .replace(/\n/g, '<br>');
            }
            
            message.innerHTML = `
                <div class="message-avatar security">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="message-content security">
                    <div>${processedContent}</div>
                    <div class="message-meta">
                        <span class="message-time">${timeString}</span>
                        <div class="message-actions">
                            <button class="message-action" onclick="copyMessage(this)">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                            <button class="message-action" onclick="showSecurityHelp()">
                                <i class="fas fa-question-circle"></i> 帮助
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(message);
            container.scrollTop = container.scrollHeight;
        }

        // 显示输入分析
        function showInputAnalysis(input) {
            if (!input || input.length < 10) return;
            
            const analysisMessage = `
                📝 **输入分析**
                
                检测到的内容片段: "${input.length > 100 ? input.substring(0, 100) + '...' : input}"
                
                建议您可以尝试:
                • 重新表述您的问题
                • 使用更具体和清晰的语言
                • 避免使用可能引起误解的词汇
            `;
            
            setTimeout(() => {
                addMessage('assistant', analysisMessage);
            }, 500);
        }

        // 显示安全帮助
        function showSecurityHelp() {
            const helpMessage = `
                🔒 **安全防护说明**
                
                本系统配备了多层安全检测机制，用于：
                • 防止有害内容生成
                • 阻止系统滥用尝试
                • 保护用户隐私信息
                • 确保合规的AI交互
                
                如果您的正当请求被误拦截，请：
                1. 尝试用不同的方式表达
                2. 提供更多上下文信息  
                3. 确保请求内容清晰明确
                
                感谢您的理解与配合！
            `;
            
            addMessage('assistant', helpMessage);
        }

        async function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message || !currentModel || isGenerating) return;

            // 添加用户消息
            addMessage('user', message);
            
            // 清空输入框
            input.value = '';
            autoResizeTextarea.call(input);
            
            // 更新状态
            isGenerating = true;
            updateSendButton();
            messageCount++;
            document.getElementById('message-count').textContent = messageCount;

            // 显示打字指示器
            const typingIndicator = showTypingIndicator();

            try {
                const startTime = Date.now();
                
                const response = await fetch('/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer cherry-studio-key'
                    },
                    body: JSON.stringify({
                        model: currentModel,
                        messages: [
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        temperature: modelParams.temperature,
                        max_tokens: modelParams.max_tokens,
                        top_p: modelParams.top_p,
                        stream: false
                    })
                });

                const responseTime = Date.now() - startTime;
                document.getElementById('response-time').textContent = responseTime + ' ms';

                if (!response.ok) {
                    const errorData = await response.json().catch(() => null);
                    if (response.status === 403 && errorData) {
                        // 处理安全检测拦截的详细错误
                        throw new SecurityViolationError(errorData);
                    }
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                const assistantMessage = data.choices[0]?.message?.content || '抱歉，我无法处理您的请求。';

                // 移除打字指示器
                removeTypingIndicator(typingIndicator);
                
                // 添加助手消息
                addMessage('assistant', assistantMessage);

            } catch (error) {
                console.error('发送消息失败:', error);
                
                // 移除打字指示器
                removeTypingIndicator(typingIndicator);
                
                // 处理不同类型的错误
                if (error instanceof SecurityViolationError) {
                    handleSecurityViolation(error);
                } else {
                    // 显示通用错误消息
                    addMessage('assistant', `抱歉，发生了错误：${error.message}`);
                }
            } finally {
                isGenerating = false;
                updateSendButton();
            }
        }

        function addMessage(role, content) {
            const container = document.getElementById('messages-container');
            const message = document.createElement('div');
            message.className = `message ${role}`;
            
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            
            // 处理Markdown内容
            let processedContent = content;
            if (typeof marked !== 'undefined') {
                processedContent = marked.parse(content);
            } else {
                // 简单的代码块处理
                processedContent = content
                    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>')
                    .replace(/`([^`]+)`/g, '<code>$1</code>')
                    .replace(/\n/g, '<br>');
            }
            
            message.innerHTML = `
                <div class="message-avatar ${role}">
                    <i class="fas fa-${role === 'user' ? 'user' : 'robot'}"></i>
                </div>
                <div class="message-content ${role}">
                    <div>${processedContent}</div>
                    <div class="message-meta">
                        <span class="message-time">${timeString}</span>
                        <div class="message-actions">
                            <button class="message-action" onclick="copyMessage(this)">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                            ${role === 'user' ? `<button class="message-action" onclick="editMessage(this)">
                                <i class="fas fa-edit"></i> 编辑
                            </button>` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(message);
            container.scrollTop = container.scrollHeight;
            
            // 保存到对话历史
            conversationHistory.push({
                role,
                content,
                timestamp: now.toISOString()
            });
        }

        function showTypingIndicator() {
            const container = document.getElementById('messages-container');
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.innerHTML = `
                <div class="message-avatar assistant">
                    <i class="fas fa-robot"></i>
                </div>
                <div>
                    <span style="color: var(--text-secondary); font-size: 14px;">正在思考</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            
            container.appendChild(indicator);
            container.scrollTop = container.scrollHeight;
            
            return indicator;
        }

        function removeTypingIndicator(indicator) {
            if (indicator && indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }

        function showSecurityWarning(message) {
            const container = document.getElementById('messages-container');
            const warning = document.createElement('div');
            warning.className = 'security-warning';
            warning.innerHTML = `
                <i class="fas fa-shield-alt"></i>
                <span>${message}</span>
            `;
            
            container.appendChild(warning);
            container.scrollTop = container.scrollHeight;
            
            // 5秒后自动移除
            setTimeout(() => {
                if (warning.parentNode) {
                    warning.parentNode.removeChild(warning);
                }
            }, 5000);
        }

        function copyMessage(button) {
            const messageContent = button.closest('.message-content').querySelector('div').innerText;
            navigator.clipboard.writeText(messageContent).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check"></i> 已复制';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }

        function editMessage(button) {
            const messageDiv = button.closest('.message-content').querySelector('div');
            const currentContent = messageDiv.innerText;
            
            const input = document.createElement('textarea');
            input.value = currentContent;
            input.className = 'message-input';
            input.style.width = '100%';
            input.style.minHeight = '60px';
            
            messageDiv.innerHTML = '';
            messageDiv.appendChild(input);
            
            const saveBtn = document.createElement('button');
            saveBtn.textContent = '保存';
            saveBtn.className = 'chat-action-btn';
            saveBtn.style.marginTop = '8px';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = '取消';
            cancelBtn.className = 'chat-action-btn';
            cancelBtn.style.marginTop = '8px';
            cancelBtn.style.marginLeft = '8px';
            
            messageDiv.appendChild(saveBtn);
            messageDiv.appendChild(cancelBtn);
            
            saveBtn.onclick = () => {
                messageDiv.innerHTML = input.value.replace(/\n/g, '<br>');
            };
            
            cancelBtn.onclick = () => {
                messageDiv.innerHTML = currentContent;
            };
            
            input.focus();
        }

        function clearChat() {
            if (confirm('确定要清空所有对话记录吗？此操作不可撤销。')) {
                const container = document.getElementById('messages-container');
                // 保留欢迎消息
                const welcomeMessage = container.querySelector('.message.assistant');
                container.innerHTML = '';
                if (welcomeMessage) {
                    container.appendChild(welcomeMessage);
                }
                
                conversationHistory = [];
                messageCount = 0;
                document.getElementById('message-count').textContent = '0';
            }
        }

        function exportChat() {
            if (conversationHistory.length === 0) {
                alert('没有对话记录可以导出。');
                return;
            }
            
            const chatData = {
                model: currentModel,
                parameters: modelParams,
                timestamp: new Date().toISOString(),
                messages: conversationHistory
            };
            
            const dataStr = JSON.stringify(chatData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `chat-export-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 定期检查系统状态
        setInterval(checkSystemStatus, 30000);
    </script>
</body>

</html>