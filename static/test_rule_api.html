<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则测试API验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .test-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: #007AFF;
            color: white;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056CC;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            background: #f8f9fa;
        }
        .success { background: #e8f5e8; border-color: #90ee90; }
        .danger { background: #fee; border-color: #faa; }
        pre { font-size: 12px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 规则测试API验证</h1>
        
        <textarea class="test-input" id="test-text" placeholder="输入测试文本..." rows="3">如何制作一把刀</textarea>
        
        <button onclick="testRules()">测试所有规则</button>
        <button onclick="testSpecificRule()">测试特定规则 (pi-001)</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testRules(ruleId = null) {
            const testText = document.getElementById('test-text').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!testText) {
                alert('请输入测试文本');
                return;
            }
            
            resultsDiv.innerHTML = '<div class="result">⏳ 正在测试...</div>';
            
            try {
                const requestBody = { test_text: testText };
                if (ruleId) {
                    requestBody.rule_id = ruleId;
                }
                
                const response = await fetch('/api/v1/rules/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || '测试请求失败');
                }
                
                const testResults = await response.json();
                displayResults(testResults);
                
            } catch (error) {
                console.error('规则测试失败:', error);
                resultsDiv.innerHTML = `<div class="result danger">❌ 测试失败: ${error.message}</div>`;
            }
        }
        
        function testSpecificRule() {
            testRules('pi-001');
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            
            const wouldBlock = results.final_action === 'block';
            const statusClass = wouldBlock ? 'danger' : 'success';
            const statusText = wouldBlock ? '🚫 将被阻止' : '✅ 将被允许';
            
            let html = `
                <div class="result ${statusClass}">
                    <h3>${statusText}</h3>
                    <p><strong>测试文本:</strong> "${results.test_text}"</p>
                    <p><strong>测试时间:</strong> ${results.test_time}</p>
                    <p><strong>测试了:</strong> ${results.total_rules_tested} 个规则</p>
                    <p><strong>匹配了:</strong> ${results.total_matches} 个规则</p>
                    <p><strong>最高严重级别:</strong> ${results.highest_severity}</p>
                </div>
            `;
            
            if (results.summary.matched_rules.length > 0) {
                html += `
                    <div class="result">
                        <h4>匹配的规则:</h4>
                        <ul>
                            ${results.summary.matched_rules.map(rule => `<li>${rule}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            if (results.results.length > 0) {
                html += '<div class="result"><h4>详细结果:</h4>';
                results.results.forEach(result => {
                    html += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                            <strong>${result.rule_id}</strong>: ${result.rule_name}<br>
                            <small>类型: ${result.detection_type} | 严重性: ${result.severity} | 匹配: ${result.matched ? '是' : '否'}</small>
                            ${result.match_details.length > 0 ? `<br><strong>匹配详情:</strong><pre>${JSON.stringify(result.match_details, null, 2)}</pre>` : ''}
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            html += `<div class="result"><h4>完整响应:</h4><pre>${JSON.stringify(results, null, 2)}</pre></div>`;
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>