# 规则管理页面前端功能测试计划

## 测试环境
- 服务器地址: http://localhost:8081
- 测试页面: /static/admin/model_rules.html
- 浏览器: Chrome/Firefox (开启开发者工具)

## 测试步骤

### 1. 页面加载和显示测试 ✅

#### 1.1 基本页面加载
- [ ] 访问 http://localhost:8081/static/admin/model_rules.html
- [ ] 检查页面是否正常加载，无404错误
- [ ] 检查页面标题是否正确显示
- [ ] 检查CSS样式是否正确加载

#### 1.2 模型卡片显示
- [ ] 检查模型网格容器是否存在
- [ ] 检查是否显示模型卡片
- [ ] 检查模型卡片的基本信息（名称、描述等）
- [ ] 检查模型卡片的操作按钮（配置、详情）

#### 1.3 数据加载检查
- [ ] 打开浏览器开发者工具 -> Network标签
- [ ] 刷新页面，检查以下API调用：
  - GET /api/v1/ollama/models (模型列表)
  - GET /api/v1/rule-templates (规则模板)
- [ ] 检查API响应状态码是否为200
- [ ] 检查返回的数据格式是否正确

### 2. 模型配置功能测试

#### 2.1 配置按钮点击
- [ ] 点击任意模型卡片的"配置"按钮（齿轮图标）
- [ ] 检查是否弹出模型配置弹窗
- [ ] 检查弹窗标题是否正确显示模型名称

#### 2.2 规则表格加载
- [ ] 在模型配置弹窗中，检查规则表格是否显示
- [ ] 检查表格头部是否正确（规则名称、类型、严重程度等）
- [ ] 检查是否加载了规则数据
- [ ] 检查Network标签，确认调用了 GET /api/v1/rules

#### 2.3 规则表格交互
- [ ] 检查规则表格中的开关按钮是否可以点击
- [ ] 检查规则表格中的编辑按钮是否存在
- [ ] 检查规则表格中的删除按钮是否存在

### 3. 规则编辑功能测试

#### 3.1 编辑按钮点击
- [ ] 在模型配置弹窗的规则表格中，点击任意规则的"编辑"按钮
- [ ] 检查是否弹出规则编辑弹窗
- [ ] 检查Network标签，确认调用了 GET /api/v1/rules/{rule_id}

#### 3.2 规则编辑弹窗显示
- [ ] 检查规则编辑弹窗是否正确显示
- [ ] 检查弹窗标题是否为"编辑规则"
- [ ] 检查所有表单字段是否存在：
  - 规则名称 (rule-name)
  - 规则描述 (rule-description)
  - 检测类型 (rule-type)
  - 严重程度 (rule-severity)
  - 优先级 (rule-priority)
  - 启用状态 (rule-enabled)
  - 模式列表 (rule-patterns)
  - 白名单 (rule-whitelist)

#### 3.3 数据填充检查
- [ ] 检查规则名称字段是否正确填充
- [ ] 检查规则描述字段是否正确填充
- [ ] 检查检测类型下拉框是否正确选中
- [ ] 检查严重程度下拉框是否正确选中
- [ ] 检查优先级字段是否正确填充
- [ ] 检查启用状态复选框是否正确设置
- [ ] 检查模式列表文本域是否正确填充
- [ ] 检查白名单文本域是否正确填充

#### 3.4 表单操作
- [ ] 尝试修改各个字段的值
- [ ] 点击"保存"按钮
- [ ] 检查是否发送了PUT请求到 /api/v1/rules/{rule_id}
- [ ] 检查保存后是否有成功提示
- [ ] 点击"取消"按钮，检查弹窗是否关闭

### 4. 规则模板功能测试

#### 4.1 模板选择
- [ ] 在规则编辑弹窗中，检查是否有模板选择功能
- [ ] 检查模板下拉框是否加载了模板数据
- [ ] 选择一个模板，检查表单是否自动填充

#### 4.2 模板应用
- [ ] 选择不同的模板，检查字段值是否正确更新
- [ ] 检查模板应用后，用户是否可以继续编辑

### 5. 错误处理和用户反馈测试

#### 5.1 网络错误处理
- [ ] 断开网络连接，尝试加载页面
- [ ] 检查是否有适当的错误提示
- [ ] 恢复网络，检查页面是否能正常工作

#### 5.2 API错误处理
- [ ] 模拟API返回错误状态码（可通过修改API地址）
- [ ] 检查是否有错误提示信息
- [ ] 检查错误提示是否用户友好

#### 5.3 表单验证
- [ ] 在规则编辑表单中，清空必填字段
- [ ] 尝试保存，检查是否有验证提示
- [ ] 输入无效数据，检查验证逻辑

### 6. 响应式设计测试

#### 6.1 不同屏幕尺寸
- [ ] 调整浏览器窗口大小，检查布局是否适应
- [ ] 检查移动设备视图下的显示效果
- [ ] 检查弹窗在小屏幕下是否正常显示

### 7. 性能测试

#### 7.1 加载性能
- [ ] 检查页面首次加载时间
- [ ] 检查JavaScript文件大小和加载时间
- [ ] 检查CSS文件大小和加载时间

#### 7.2 交互性能
- [ ] 检查点击按钮的响应时间
- [ ] 检查弹窗打开/关闭的动画流畅度
- [ ] 检查大量数据加载时的性能

## 测试结果记录

### 发现的问题
1. 
2. 
3. 

### 需要修复的功能
1. 
2. 
3. 

### 优化建议
1. 
2. 
3. 
